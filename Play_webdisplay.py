import os
import re
import time
import threading
import webview
import json
import sys
import signal
import socket
import logging
import cv2
import numpy as np
import yaml
from pathlib import Path
from multiprocessing import Process
from typing import Dict, List, Optional, Any, Set

# 新增导入：用于WebSocket和HTTP服务器
import asyncio
import websockets
from http.server import SimpleHTTPRequestHandler
import socketserver

# --- 集中式日志配置 ---
_PLAY_WEBDISPLAY_ROOT_LOGGER_NAME = "PlayWebDisplay"
_play_webdisplay_logger_configured = False
_logger_configuration_lock = threading.Lock()

def _ensure_webdisplay_logging_configured():
    """配置PlayWebDisplay模块的根日志记录器（如果尚未配置）"""
    global _play_webdisplay_logger_configured
    # 快速检查（无锁）
    if _play_webdisplay_logger_configured:
        return

    with _logger_configuration_lock:
        # 锁内二次检查
        if _play_webdisplay_logger_configured:
            return

        logger = logging.getLogger(_PLAY_WEBDISPLAY_ROOT_LOGGER_NAME)
        logger.setLevel(logging.DEBUG)  # 为所有webdisplay日志记录器设置基础级别

        # 防止多次添加处理器
        if not logger.handlers:
            formatter = logging.Formatter('%(asctime)s - [%(name)s] - %(levelname)s - %(message)s')

            # 控制台处理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.INFO)  # 控制台输出可以不那么详细
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)

            # 文件处理器
            try:
                log_file_path = Path(__file__).parent / "webdisplay.log"
                file_handler = logging.FileHandler(log_file_path, encoding='utf-8', mode='a')
                file_handler.setLevel(logging.DEBUG)  # 文件可以更详细
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
            except Exception as e:
                print(f"Play_webdisplay: 严重错误 - 无法创建日志文件 'webdisplay.log': {e}", file=sys.stderr)
        
        # 防止PlayWebDisplay日志记录器的消息传播到绝对根日志记录器
        # 这可以避免在主应用程序也配置了根日志记录器处理器时出现重复消息
        logger.propagate = False
        
        _play_webdisplay_logger_configured = True

def get_logger(name: str) -> logging.Logger:
    """
    返回PlayWebDisplay内部组件的日志记录器实例。
    确保PlayWebDisplay的根日志记录器只配置一次。
    返回的日志记录器将是PlayWebDisplay根日志记录器的子日志记录器。
    """
    _ensure_webdisplay_logging_configured()
    # 构造日志记录器名称，如 "PlayWebDisplay.ClassName"
    return logging.getLogger(f"{_PLAY_WEBDISPLAY_ROOT_LOGGER_NAME}.{name}")

# 替换旧的模块级日志记录器初始化
module_logger = get_logger('ModuleRoot')

class WebDisplayServer:
    """基于Socket的TCP服务器，接收主进程的显示指令"""
    
    def __init__(self, port, web_display_process):
        self.port = port
        self.web_display_process = web_display_process
        self.server_socket = None
        self.is_running = True
        self.logger = get_logger('WebDisplayServer')
        
        # 命令处理器映射
        self.command_handlers = {
            'show_message': self._handle_show_message,
            'show_status_message': self._handle_show_status_message,
            'update_queue': self._handle_update_queue,
            'clear_text': self._handle_clear_text,
            'exit': self._handle_exit,
            'update_detection_display': self._handle_update_detection_display,
            'update_config': self._handle_update_config,
            'custom_js': self._handle_custom_js,
        }
        
    def start(self):
        """启动TCP服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('localhost', self.port))
            self.server_socket.listen(1)
            self.server_socket.settimeout(1.0)  # 设置超时以便能够检查运行状态
            
            self.logger.info(f"WebView TCP服务器已启动，监听端口 {self.port}")
            
            while self.is_running:
                try:
                    client_socket, address = self.server_socket.accept()
                    self.logger.info(f"接受来自 {address} 的连接")
                    
                    # 在新线程中处理客户端
                    client_thread = threading.Thread(
                        target=self._handle_client, 
                        args=(client_socket,), 
                        daemon=True
                    )
                    client_thread.start()
                    
                except socket.timeout:
                    continue
                except Exception as e:
                    if self.is_running:
                        self.logger.error(f"接受连接时出错: {e}")
                    break
                    
        except Exception as e:
            self.logger.error(f"启动TCP服务器失败: {e}")
        finally:
            if self.server_socket:
                self.server_socket.close()
                
    def _handle_client(self, client_socket):
        """处理客户端连接"""
        try:
            buffer = ""
            while self.is_running: # Check instance's is_running flag
                data = client_socket.recv(4096).decode('utf-8')
                if not data:
                    break
                    
                buffer += data
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    if line:
                        self._process_command(line)
                        
        except ConnectionResetError:
            self.logger.info("客户端连接已重置")
        except socket.timeout:
            self.logger.warning("客户端套接字读取超时")
        except Exception as e:
            # 只有当服务器仍在运行时，才记录错误，避免在正常关闭时产生不必要的错误日志
            if self.is_running:
                 self.logger.error(f"处理客户端时出错: {e}", exc_info=True)
        finally:
            try:
                client_socket.shutdown(socket.SHUT_RDWR)
            except (OSError, socket.error):
                pass # 忽略套接字已关闭等错误
            client_socket.close()
            self.logger.info("客户端连接已关闭")
            
    def _process_command(self, command_line):
        """处理接收到的命令，增强负载，并转发到WebSocket和本地窗口"""
        action_for_log = "N/A"  # 默认值，用于记录日志，以防早期失败
        try:
            command = json.loads(command_line)
            action = command.get('action')
            action_for_log = action if action else "N/A"  # 更新用于日志记录的值

            # 默认情况下，最终命令就是收到的命令
            final_command = command

            # 如果是检测更新，则准备一个包含ROI边界和变换后物体的增强负载
            if action == 'update_detection_display':
                objects_data_raw = command.get('data', [])
                self.logger.debug(f"为WebSocket广播增强检测数据: {len(objects_data_raw)} 个物体")

                grab_area_payload = None
                if self.web_display_process and self.web_display_process.grab_area_config.get('enabled'):

                    transformed_objects = []
                    # 仅当存在变换矩阵时才尝试变换物体
                    if self.web_display_process.perspective_transform_matrix is not None:
                        for obj_raw in objects_data_raw:
                            # 使用与本地版一致的逻辑，对归一化包围框进行变换
                            bbox_norm = obj_raw.get('bounding_box_xyxy_normalized_prop')
                            if not bbox_norm:
                                bbox_norm = obj_raw.get('box_xyxyn')

                            if bbox_norm and len(bbox_norm) == 4:
                                try:
                                    src_pts_bbox = np.array([
                                        [float(bbox_norm[0]), float(bbox_norm[1])],  # 左上
                                        [float(bbox_norm[2]), float(bbox_norm[1])],  # 右上
                                        [float(bbox_norm[2]), float(bbox_norm[3])],  # 右下
                                        [float(bbox_norm[0]), float(bbox_norm[3])]   # 左下
                                    ], dtype=np.float32).reshape(-1, 1, 2)

                                    # 应用透视变换
                                    dst_pts_bbox = cv2.perspectiveTransform(src_pts_bbox, self.web_display_process.perspective_transform_matrix)

                                    if dst_pts_bbox is not None:
                                        # 计算变换后的轴对齐包围框
                                        min_x = float(np.min(dst_pts_bbox[:, 0, 0]))
                                        max_x = float(np.max(dst_pts_bbox[:, 0, 0]))
                                        min_y = float(np.min(dst_pts_bbox[:, 0, 1]))
                                        max_y = float(np.max(dst_pts_bbox[:, 0, 1]))

                                        axis_aligned_bbox = [min_x, min_y, max_x, max_y]

                                        # 创建一个包含原始信息和变换后信息的新对象
                                        transformed_obj = obj_raw.copy()
                                        transformed_obj['bbox_transformed_axis_aligned'] = axis_aligned_bbox
                                        transformed_objects.append(transformed_obj)
                                except (ValueError, TypeError) as e:
                                    self.logger.warning(f"处理包围框坐标时出错: {e}, bbox_norm: {bbox_norm}")
                                    continue

                    # 构造完整的负载
                    grab_area_payload = {
                        'objects': transformed_objects,
                        'roi_boundary_points_transformed': self.web_display_process.roi_points_transformed_for_js,
                        'config': self.web_display_process.grab_area_config
                    }

                    # 用这个完整的负载替换原始命令的数据部分，形成新的最终命令
                    final_command = {
                        'action': 'update_detection_display',
                        'data': grab_area_payload
                    }

            # 将最终的命令（可能是增强后的）转发给WebSocket客户端
            if self.web_display_process:
                self.web_display_process.forward_command_to_websockets(final_command)

            # 如果本地窗口存在，仍然可以更新它（可选）
            # 注意：本地处理器现在也应该接收增强后的命令，以保持一致
            if self.web_display_process and self.web_display_process.window:
                handler = self.command_handlers.get(action)
                if handler:
                    # 传递增强后的命令，本地窗口的处理器也需要使用这些数据
                    handler(final_command)
                else:
                    self.logger.warning(f"未找到命令 '{action}' 的本地窗口处理器")

        except json.JSONDecodeError as e:
            self.logger.error(f"解析命令失败: {e}, 命令: {command_line}")
        except Exception as e:
            self.logger.error(f"处理命令 '{action_for_log}' 时发生意外错误: {e}", exc_info=True)

    # --- 命令处理方法 ---
    def _handle_show_message(self, command: Dict[str, Any]):
        """处理 'show_message' 命令"""
        text = command.get('text', '')
        duration = command.get('duration')  # 获取duration
        animated = command.get('animated', False)
        
        self.logger.debug(f"处理显示消息命令: {text[:50]}{'...' if len(text) > 50 else ''}, duration: {duration}")
        
        if self.web_display_process.window:
            # 将 duration 传递给 JavaScript
            js_code = f'updateText({json.dumps(text)}, {str(animated).lower()}, {json.dumps(duration)});'
            self.web_display_process.window.evaluate_js(js_code)
            
            # Python端不再处理延迟清除
            # 删除原有的延迟清除线程逻辑

    def _handle_show_status_message(self, command: Dict[str, Any]):
        """处理 'show_status_message' 命令"""
        status_type = command.get('status_type', 'playing')
        player_name = command.get('player_name', '玩家')
        item_name = command.get('item_name', '')
        success_count = command.get('success_count')  # 新增：获取成功次数

        # 从 display_settings 获取对应状态的 duration
        duration = None
        if self.web_display_process and self.web_display_process.full_config_for_html:
            display_settings = self.web_display_process.full_config_for_html.get('web_display', {}).get('display_settings', {})
            status_config = display_settings.get(status_type, {})
            duration = status_config.get('duration')  # duration 可能为 null 或数字

        self.logger.debug(f"处理状态消息: {status_type}, 玩家: {player_name}, success_count: {success_count}, duration: {duration}")

        if self.web_display_process.window:
            # 将 success_count 和 duration 传递给 JavaScript
            js_code = f'updateStatusMessage({json.dumps(status_type)}, {json.dumps(player_name)}, {json.dumps(item_name)}, {json.dumps(success_count)});'
            self.web_display_process.window.evaluate_js(js_code)

    def _handle_update_queue(self, command: Dict[str, Any]):
        """处理 'update_queue' 命令"""
        queue_data = command.get('data', [])
        self.logger.debug(f"处理队列更新命令: {len(queue_data)} 项")
        
        if self.web_display_process.window:
            js_code = f'updateQueueDisplay({json.dumps(queue_data, ensure_ascii=False)});'
            self.web_display_process.window.evaluate_js(js_code)

    def _handle_clear_text(self, command: Dict[str, Any]):
        """处理 'clear_text' 命令"""
        self.logger.debug("处理清除文本命令")
        if self.web_display_process.window:
            self.web_display_process.window.evaluate_js('updateText("", false);')

    def _handle_exit(self, command: Dict[str, Any]):
        """处理 'exit' 命令"""
        self.logger.info("收到退出命令，将停止服务器并销毁窗口")
        self.is_running = False # 设置服务器停止标志
        if self.web_display_process.window:
            try:
                self.web_display_process.window.destroy()
            except Exception as e:
                self.logger.warning(f"销毁窗口时出错: {e}")
        # 触发WebDisplayProcess的关闭逻辑，它会停止TCP服务器
        if self.web_display_process:
            self.web_display_process.should_close = True 
            # WebDisplayServer的stop方法会在其主循环退出时或WebDisplayProcess关闭时被调用

    def _handle_update_detection_display(self, command: Dict[str, Any]):
        """处理 'update_detection_display' 命令，数据已经被_process_command预处理"""
        # 数据已经被上游的 _process_command 处理，这里直接使用
        grab_area_payload = command.get('data')

        if self.web_display_process and self.web_display_process.window:
            if grab_area_payload and isinstance(grab_area_payload, dict):
                self.logger.debug(f"本地窗口更新抓取区域: {len(grab_area_payload.get('objects', []))} 个物体")
                js_code = f'updateGrabAreaDisplay({json.dumps(grab_area_payload, ensure_ascii=False)});'
                self.web_display_process.window.evaluate_js(js_code)
            else:
                # 如果没有负载（例如，抓取区域被禁用），也发送一个空更新来清空画布
                empty_payload = {
                    'objects': [],
                    'roi_boundary_points_transformed': self.web_display_process.roi_points_transformed_for_js,
                    'config': self.web_display_process.grab_area_config
                }
                js_code = f'updateGrabAreaDisplay({json.dumps(empty_payload, ensure_ascii=False)});'
                self.web_display_process.window.evaluate_js(js_code)

    def _handle_update_config(self, command: Dict[str, Any]):
        """处理 'update_config' 命令"""
        new_config = command.get('config', {})
        full_config = command.get('full_config', {})

        self.logger.info(f"处理配置更新命令，更新参数: {list(new_config.keys())}")

        try:
            # 更新 WebDisplayProcess 的配置
            if self.web_display_process:
                # 更新本地配置
                self.web_display_process.config.update(new_config)
                self.web_display_process.full_config_for_html = full_config

                # 如果抓取区域配置发生变化，重新初始化透视变换
                if 'grab_area' in new_config:
                    self.web_display_process.grab_area_config = self.web_display_process.config.get('grab_area', {})
                    self.web_display_process._initialize_perspective_transform()
                    self.logger.info("抓取区域配置已更新，透视变换已重新初始化")

                # 通知前端配置已更新（如果需要的话）
                if self.web_display_process.window:
                    # 可以发送配置更新事件到前端JavaScript
                    js_code = f'if(typeof onConfigUpdate === "function") {{ onConfigUpdate({json.dumps(new_config, ensure_ascii=False)}); }}'
                    self.web_display_process.window.evaluate_js(js_code)

                self.logger.info("Web显示进程配置更新完成")

        except Exception as e:
            self.logger.error(f"更新Web显示进程配置时出错: {e}")

    def _handle_custom_js(self, command: Dict[str, Any]):
        """处理自定义JavaScript代码执行命令"""
        js_code = command.get('js_code', '')
        if not js_code:
            self.logger.warning("收到空的JavaScript代码")
            return

        self.logger.debug(f"执行自定义JavaScript: {js_code[:100]}{'...' if len(js_code) > 100 else ''}")

        if self.web_display_process and self.web_display_process.window:
            try:
                self.web_display_process.window.evaluate_js(js_code)
            except Exception as e:
                self.logger.error(f"执行JavaScript代码时出错: {e}")

class WebDisplayProcess:
    """独立进程运行的Web显示，通过TCP Socket接收指令"""
    
    def _signal_handler(self, sig, frame):
        """处理进程退出信号，优雅关闭窗口和服务器"""
        self.logger.info(f"收到信号 {sig}，正在关闭 WebDisplay...")
        self.should_close = True

        # 新增：关闭异步任务
        if self.main_task and not self.main_task.done():
            self.main_task.cancel()

        if self.window:
            try:
                self.window.destroy()
            except Exception as e:
                self.logger.warning(f"关闭 WebView 窗口时出错: {e}")
        if self.tcp_server:
            try:
                self.tcp_server.is_running = False  # 修改：使用 is_running 而不是 stop()
            except Exception as e:
                self.logger.warning(f"停止 TCP 服务器时出错: {e}")
    
    def __init__(self, config_dict: Dict[str, Any]):
        self.logger = get_logger('WebDisplayProcess')
        self.current_dir = Path(__file__).parent
        self.config = config_dict.get('web_display', {})
        self.full_config_for_html = config_dict

        # 新增：WebSocket 和 HTTP 相关属性
        self.websocket_port = self.config.get('websocket_port', 5560)
        self.http_config = self.config.get('http_server', {})
        self.http_port = self.http_config.get('port', 5559)
        self.connected_websockets: Set[Any] = set()  # WebSocket连接集合
        self.main_task: Optional[asyncio.Task] = None
        self.http_server_thread: Optional[threading.Thread] = None

        # 抓取区域可视化相关属性
        self.grab_area_config = self.config.get('grab_area', {})
        self.perspective_transform_matrix = None
        self.roi_points_from_livecam = None
        self.roi_points_transformed_for_js = None

        # 加载摄像头配置和初始化透视变换
        self._load_livecam_config()
        self._initialize_perspective_transform()

        self.tcp_port = self.config.get('tcp_port', self.config.get('websocket_port', 5558))  # TCP指令端口
        self.tcp_server: Optional[WebDisplayServer] = None

        self.window: Optional[webview.Window] = None
        self.current_text = ""
        self.should_close = False

        if not self.config.get('enabled', False):
            self.logger.info("Web显示功能在配置中未启用")
            return

        # 仅在启用且在主线程时注册信号处理器
        try:
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
        except ValueError:
            # 如果不在主线程中，signal.signal会抛出ValueError
            self.logger.debug("不在主线程中，跳过信号处理器注册")

    def _load_livecam_config(self):
        """加载 Config_livecam.yaml 中的 roi_points"""
        # 尝试在父目录和当前目录查找 Config_livecam.yaml
        livecam_config_path = self.current_dir.parent / 'Config_livecam.yaml'
        if not livecam_config_path.exists():
            livecam_config_path = self.current_dir / 'Config_livecam.yaml'

        if livecam_config_path.exists():
            try:
                with open(livecam_config_path, 'r', encoding='utf-8') as f:
                    livecam_config_data = yaml.safe_load(f)
                self.roi_points_from_livecam = livecam_config_data.get('roi_points')
                
                if self.roi_points_from_livecam and isinstance(self.roi_points_from_livecam, list) and len(self.roi_points_from_livecam) == 4:
                    # 验证点格式
                    valid_points = True
                    for point in self.roi_points_from_livecam:
                        if not (isinstance(point, list) and len(point) == 2 and all(isinstance(coord, (int, float)) for coord in point)):
                            valid_points = False
                            break
                    if valid_points:
                        self.logger.info(f"成功加载roi_points: {self.roi_points_from_livecam}")
                    else:
                        self.logger.warning(f"roi_points格式不正确，应为4个[x,y]坐标对的列表")
                        self.roi_points_from_livecam = None
                else:
                    self.logger.warning(f"未找到有效的roi_points或格式不正确")
                    self.roi_points_from_livecam = None
            except Exception as e:
                self.logger.error(f"加载Config_livecam.yaml时出错: {e}")
                self.roi_points_from_livecam = None
        else:
            self.logger.warning(f"未找到Config_livecam.yaml文件，抓取区域可视化功能将受限")

    def _initialize_perspective_transform(self):
        """初始化透视变换矩阵"""
        object_display_cfg = self.grab_area_config.get('object_display', {})
        if not object_display_cfg.get('enabled'):
            self.logger.info("物体显示未启用，不计算透视变换矩阵")
            return

        if not self.roi_points_from_livecam:
            self.logger.warning("无法初始化透视变换：roi_points_from_livecam不可用")
            return

        source_pts_norm_list = self.grab_area_config.get('source_normalized_points')
        if not source_pts_norm_list or not isinstance(source_pts_norm_list, list) or len(source_pts_norm_list) != 4:
            self.logger.warning("source_normalized_points配置不正确，需要4个[x,y]坐标对的列表")
            return
        
        # 验证source_normalized_points格式
        valid_src_points = True
        for point in source_pts_norm_list:
            if not (isinstance(point, list) and len(point) == 2 and all(isinstance(coord, (int, float)) for coord in point)):
                valid_src_points = False
                break
        if not valid_src_points:
            self.logger.warning("source_normalized_points格式不正确")
            return

        # 修复类型问题：正确构造NumPy数组
        try:
            pts_src = np.array(source_pts_norm_list, dtype=np.float32)
            pts_dst = np.array(self.roi_points_from_livecam, dtype=np.float32)
        except (ValueError, TypeError) as e:
            self.logger.error(f"转换坐标点为NumPy数组时出错: {e}")
            return

        try:
            self.perspective_transform_matrix = cv2.getPerspectiveTransform(pts_src, pts_dst)
            self.logger.info("透视变换矩阵计算成功")
            # 强制把 roi 点转为 Python float，避免 JSON 序列化问题
            flat = pts_dst.reshape(-1, 2)
            self.roi_points_transformed_for_js = [
                [float(x), float(y)] for x, y in flat
            ]
        except cv2.error as e:
            self.logger.error(f"OpenCV计算透视变换矩阵时出错: {e}")
            self.perspective_transform_matrix = None
            self.roi_points_transformed_for_js = None
        except Exception as e:
            self.logger.error(f"计算透视变换矩阵时出现未知错误: {e}")
            self.perspective_transform_matrix = None
            self.roi_points_transformed_for_js = None

    def get_font_format(self, font_filename):
        """根据字体文件名后缀确定字体格式"""
        if not font_filename:
            return 'truetype'
            
        ext = Path(font_filename).suffix.lower()
        if ext == '.ttf':
            return 'truetype'
        elif ext == '.otf':
            return 'opentype'
        elif ext == '.woff':
            return 'woff'
        elif ext == '.woff2':
            return 'woff2'
        elif ext == '.ttc':
            return 'truetype'  # TTC 文件是 TrueType 字体集合
        return 'truetype'  # 默认为truetype

    def _generate_font_face_css_and_families(self, config_name_prefix: str, font_config: Dict[str, Any], default_font_family: str = "'Microsoft YaHei', sans-serif"):
        """生成@font-face CSS规则和有效的font-family字符串"""
        regular_font_file = font_config.get('regular_font_file')
        emoji_font_file = font_config.get('emoji_font_file')
        
        font_faces_css_parts = []
        css_regular_font_name = f"'{config_name_prefix}RegularCustomFont'"
        css_emoji_font_name = f"'{config_name_prefix}EmojiCustomFont'"
        
        effective_regular_font_family_list = [default_font_family]
        effective_emoji_font_family_list = [default_font_family]

        if regular_font_file and (self.current_dir / regular_font_file).is_file():
            font_format = self.get_font_format(regular_font_file)
            font_faces_css_parts.append(f"""
            @font-face {{
                font-family: {css_regular_font_name};
                src: url('./{regular_font_file}') format('{font_format}');
            }}""")
            effective_regular_font_family_list.insert(0, css_regular_font_name)
        
        if emoji_font_file and (self.current_dir / emoji_font_file).is_file():
            font_format = self.get_font_format(emoji_font_file)
            font_faces_css_parts.append(f"""
            @font-face {{
                font-family: {css_emoji_font_name};
                src: url('./{emoji_font_file}') format('{font_format}');
            }}""")
            effective_emoji_font_family_list.insert(0, css_emoji_font_name)
            if css_regular_font_name != default_font_family:
                effective_emoji_font_family_list.insert(1, css_regular_font_name)
            
        return "".join(font_faces_css_parts), ", ".join(effective_regular_font_family_list), ", ".join(effective_emoji_font_family_list)

    def create_html_content(self):
        """从模板文件创建HTML内容并替换占位符"""
        try:
            with open(self.current_dir / "webdisplay_template.html", 'r', encoding='utf-8') as f:
                template_content = f.read()
        except FileNotFoundError:
            self.logger.error("webdisplay_template.html template not found!")
            return "<html><body>Error: Template 'webdisplay_template.html' not found.</body></html>"
        except Exception as e:
            self.logger.error(f"Error reading webdisplay_template.html: {e}")
            return f"<html><body>Error reading template: {e}</body></html>"

        cfg = self.config
        display_settings_config = cfg.get('display_settings', {})
        queue_cfg = cfg.get('queue_display', {})
        default_body_font = "'Microsoft YaHei', sans-serif"

        # 修改：从新的、更简单的 'center' 配置生成CSS
        text_wrapper_pos = display_settings_config.get('position', {})
        center_coords = text_wrapper_pos.get('center')

        if isinstance(center_coords, list) and len(center_coords) == 2:
            # 如果配置了 center: [x, y]，则使用它
            left_val, top_val = center_coords[0], center_coords[1]
        else:
            # 否则，使用默认值或旧的top/left值作为备用
            left_val = text_wrapper_pos.get('left', '50%')
            top_val = text_wrapper_pos.get('top', '50%')

        # 新增：读取宽度配置，并提供一个合理的默认值
        width_val = text_wrapper_pos.get('width', '90%')

        # 将简单的配置 "翻译" 成浏览器需要的CSS
        # transform 的值现在是固定的，不再需要配置
        text_wrapper_css = f"""
            top: {top_val};
            left: {left_val};
            width: {width_val};
            transform: translate(-50%, -50%);
        """

        # 新增：创建一个适用于JS的配置副本，以保持JS端的兼容性
        import copy
        js_display_settings = copy.deepcopy(display_settings_config)

        # 添加WebSocket端口信息到JS配置中
        js_display_settings['websocket_port'] = self.websocket_port

        if 'caught_item' in js_display_settings:
            caught_item_cfg = js_display_settings.get('caught_item', {})
            main_line_cfg = caught_item_cfg.get('main_line', {})
            sub_lines_cfg = caught_item_cfg.get('sub_lines', {})

            # 将 main_line 的内容提升到 caught_item 顶层，并保留 sub_lines
            js_display_settings['caught_item'] = main_line_cfg
            js_display_settings['caught_item']['sub_lines'] = sub_lines_cfg

        replacements = {
            '{{WINDOW_TITLE}}': cfg.get("window_title", "文本显示"),
            '{{BACKGROUND_COLOR}}': cfg.get("background_color", "rgba(0,0,0,0)"),
            # 修改：使用处理过的配置传给JS
            '{{DISPLAY_SETTINGS_JSON}}': json.dumps(js_display_settings, ensure_ascii=False),
            '{{ESCAPED_INITIAL_TEXT}}': json.dumps(self.current_text or ""),
            '{{BODY_DEFAULT_FONT_FAMILY}}': default_body_font,
            '{{GRAB_AREA_CONFIG_JSON}}': json.dumps(self.grab_area_config or {}),
            # 新增：注入queue_display配置供JavaScript使用
            '{{QUEUE_DISPLAY_CONFIG_JSON}}': json.dumps(queue_cfg or {}),
            # 新增：注入位置CSS
            '{{TEXT_WRAPPER_POSITION_CSS}}': text_wrapper_css,
            # 新增：z-index配置
            '{{GRAB_AREA_CANVAS_Z_INDEX}}': str(cfg.get('grab_area_canvas_z_index', 30)),
            '{{TEXT_WRAPPER_Z_INDEX}}': str(cfg.get('text_wrapper_z_index', 20)),
        }

        all_font_faces_parts = []

        # --- 主要修改：不再为状态消息生成静态CSS，只提取字体定义 ---
        style_definitions = [
            {"name_prefix": "Playing", "config": display_settings_config.get('playing', {})},
            {"name_prefix": "CaughtNothing", "config": display_settings_config.get('caught_nothing', {})},
            {"name_prefix": "CaughtItem", "config": display_settings_config.get('caught_item', {}).get('main_line', {})},
        ]

        for style_def in style_definitions:
            prefix = style_def["name_prefix"]
            style_config = style_def["config"]

            faces_css, reg_family, _ = self._generate_font_face_css_and_families(
                prefix, style_config, default_body_font
            )
            all_font_faces_parts.append(faces_css)

            if prefix == "Playing":
                replacements['{{BODY_DEFAULT_FONT_FAMILY}}'] = reg_family

        # --- 处理子行字体定义（不生成CSS样式） ---
        caught_item_config = display_settings_config.get('caught_item', {})
        sub_lines_config = caught_item_config.get('sub_lines', {})
        lines_config = sub_lines_config.get('lines', [])

        for i, line_config in enumerate(lines_config[:2]):  # 最多处理2个子行
            if not line_config.get('enabled', False):
                continue # 如果未启用，则跳过

            # 只生成字体配置，不生成CSS样式
            sub_line_font_config = {
                'regular_font_file': line_config.get('font_file'),
                'emoji_font_file': None # 子行通常不需要独立的emoji字体
            }
            sub_faces_css, _, _ = self._generate_font_face_css_and_families(
                f'SubLine{i}', sub_line_font_config, default_body_font
            )
            all_font_faces_parts.append(sub_faces_css)

        # --- 处理队列字体定义 ---
        if queue_cfg.get('enabled'):
            queue_font_config = {
                'regular_font_file': queue_cfg.get('regular_font_file'),
                'emoji_font_file': queue_cfg.get('emoji_font_file')
            }
            queue_faces_css, queue_reg_family, queue_emo_family = self._generate_font_face_css_and_families(
                'Queue', queue_font_config, default_body_font
            )
            all_font_faces_parts.append(queue_faces_css)
            # 队列样式仍然可以静态生成，因为它不涉及多种状态切换
            queue_styles = self._generate_queue_styles(queue_cfg, queue_reg_family, queue_emo_family)
        else:
            queue_styles = ""

        # 注入字体定义和队列样式
        replacements['{{ALL_FONT_FACES}}'] = "\n".join(filter(None, all_font_faces_parts))
        replacements['{{QUEUE_STYLES_BLOCK}}'] = queue_styles
        # 注意：我们不再生成 .status-* 相关的CSS，也不再生成 sub-line 的CSS
        replacements['{{SUB_LINE_STYLES_BLOCK}}'] = ""  # 清空

        # 执行所有替换
        processed_html = template_content
        for placeholder, value in replacements.items():
            processed_html = processed_html.replace(placeholder, str(value))
            
        return processed_html

    # --- 新增：WebSocket 和 HTTP 服务器逻辑 ---

    async def _websocket_handler(self, websocket):
        """处理新的WebSocket连接"""
        self.logger.info(f"OBS浏览器源或其他WebSocket客户端已连接: {websocket.remote_address}")
        self.connected_websockets.add(websocket)
        try:
            # 保持连接打开，等待服务器推送消息
            await websocket.wait_closed()
        except websockets.exceptions.ConnectionClosed:
            self.logger.info(f"WebSocket客户端断开连接: {websocket.remote_address}")
        except Exception as e:
            self.logger.warning(f"WebSocket连接处理异常: {e}")
        finally:
            if websocket in self.connected_websockets:
                self.connected_websockets.remove(websocket)

    async def _broadcast_to_websockets(self, message: Dict[str, Any]):
        """将消息广播到所有连接的WebSocket客户端"""
        if not self.connected_websockets:
            return

        message_json = json.dumps(message, ensure_ascii=False)
        # 使用 asyncio.gather 并发发送
        tasks = [client.send(message_json) for client in self.connected_websockets]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理发送失败的连接
        failed_clients = []
        for client, result in zip(list(self.connected_websockets), results):
            if isinstance(result, Exception):
                self.logger.warning(f"向客户端 {client.remote_address} 发送消息失败: {result}, 将其移除。")
                failed_clients.append(client)

        # 移除失败的连接
        for client in failed_clients:
            if client in self.connected_websockets:
                self.connected_websockets.remove(client)

    def _start_http_server(self):
        """在单独的线程中启动简单的HTTP服务器"""
        if not self.http_config.get('enabled', False):
            self.logger.info("配置中未启用HTTP服务器。")
            return

        def run_server():
            # 切换工作目录到脚本所在目录，这样HTML文件中的相对路径（如字体文件）才能正确解析
            original_cwd = os.getcwd()
            try:
                os.chdir(self.current_dir)

                # 创建一个特定的处理类，减少日志输出
                class CustomHTTPHandler(SimpleHTTPRequestHandler):
                    def log_message(self, format_str, *args):
                        # 减少HTTP服务器的日志输出
                        pass

                with socketserver.TCPServer(("", self.http_port), CustomHTTPHandler) as httpd:
                    self.logger.info(f"为OBS启动的HTTP服务器正在运行于端口: {self.http_port}")
                    self.logger.info(f"请在OBS浏览器源中访问: http://127.0.0.1:{self.http_port}/webdisplay.html")
                    httpd.serve_forever()
            except Exception as e:
                self.logger.error(f"HTTP服务器启动失败: {e}")
            finally:
                # 恢复原始工作目录
                os.chdir(original_cwd)

        self.http_server_thread = threading.Thread(target=run_server, daemon=True)
        self.http_server_thread.start()

    async def _main_async_loop(self):
        """运行WebSocket服务器的主异步循环"""
        self.logger.info(f"正在启动WebSocket服务器，监听端口 {self.websocket_port}")
        try:
            async with websockets.serve(self._websocket_handler, "0.0.0.0", self.websocket_port):
                await asyncio.Future()  # 永远运行，直到被取消
        except asyncio.CancelledError:
            self.logger.info("WebSocket服务器任务被取消，正在关闭。")
        except Exception as e:
            self.logger.error(f"WebSocket服务器发生致命错误: {e}", exc_info=True)

    def forward_command_to_websockets(self, command: Dict[str, Any]):
        """将指令安全地转发到WebSocket广播"""
        if self.main_task and not self.main_task.done():
            loop = self.main_task.get_loop()
            # 将异步广播函数提交到正在运行的事件循环中
            asyncio.run_coroutine_threadsafe(self._broadcast_to_websockets(command), loop)

    def start_tcp_server(self) -> bool:
        """启动TCP服务器"""
        try:
            if not self.config.get('enabled', False):
                self.logger.info("Web显示功能未启用，不启动TCP服务器")
                return False
            
            self.tcp_server = WebDisplayServer(self.tcp_port, self)
            server_thread = threading.Thread(target=self.tcp_server.start, daemon=True)
            server_thread.start()
            
            # 给服务器一点时间启动
            start_timeout = self.config.get('server_start_timeout_seconds', 1.0)
            start_check_interval = 0.1
            checks = int(start_timeout / start_check_interval)
            
            # 等待并验证服务器是否正常启动
            for _ in range(checks):
                if not server_thread.is_alive():
                    self.logger.error("TCP服务器线程意外终止")
                    return False
                
                # 尝试建立测试连接来验证服务器是否真的在监听
                try:
                    test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    test_socket.settimeout(0.1)
                    test_socket.connect(('localhost', self.tcp_port))
                    test_socket.close()
                    self.logger.info(f"TCP服务器成功启动，端口: {self.tcp_port}")
                    return True
                except (socket.error, ConnectionRefusedError):
                    time.sleep(start_check_interval)
                    continue
                except Exception as e:
                    self.logger.error(f"验证TCP服务器时发生未知错误: {e}")
                    return False
            
            self.logger.error(f"TCP服务器启动超时 ({start_timeout}秒)")
            return False
            
        except Exception as e:
            self.logger.error(f"启动TCP服务器失败: {e}", exc_info=True)
            return False

    def run(self):
        """运行web显示进程，包括TCP指令服务器、WebSocket服务器、HTTP服务器和可选的本地窗口"""
        if not self.config.get('enabled', False):
            self.logger.info("Web显示功能未启用")
            return

        # 1. 启动TCP指令服务器（保持不变，但现在它的任务是转发指令到WebSocket）
        if not self.start_tcp_server():
            self.logger.error("TCP指令服务器启动失败，Web显示进程无法继续。")
            return

        # 2. 启动HTTP服务器
        self._start_http_server()

        # 注册信号处理器（仅在主线程中）
        try:
            signal.signal(signal.SIGINT, self._signal_handler)
            signal.signal(signal.SIGTERM, self._signal_handler)
        except ValueError:
            # 如果不在主线程中，signal.signal会抛出ValueError
            self.logger.debug("不在主线程中，跳过信号处理器注册")

        # 3. 准备并启动异步服务 (WebSocket) 和可选的本地窗口
        try:
            # 4. 检查是否需要创建本地pywebview窗口
            local_window_config = self.config.get("local_window", {})
            show_local_window = local_window_config.get("enabled", True)

            if show_local_window:
                # 启动异步事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                self.main_task = loop.create_task(self._main_async_loop())
                html_content = self.create_html_content()
                if "Error:" in html_content:
                    self.logger.error(f"生成HTML时出错: {html_content[:200]}")
                else:
                    class _WebviewApi:
                        def test(self):
                            return "API测试成功"
                    api = _WebviewApi()

                    self.window = webview.create_window(
                        title=local_window_config.get("window_title", "文本显示 [本地预览]"),
                        html=html_content,
                        width=local_window_config.get("window_width", 800),
                        height=local_window_config.get("window_height", 600),
                        resizable=True,
                        on_top=local_window_config.get("on_top", False),
                        frameless=local_window_config.get("frameless", False),
                        js_api=api
                    )
                    self.logger.info("启动本地预览窗口...")

                    # pywebview.start() 会阻塞，所以我们需要在另一个线程中运行asyncio循环
                    loop_thread = threading.Thread(target=loop.run_until_complete, args=(self.main_task,), daemon=True)
                    loop_thread.start()
                    webview.start(debug=self.config.get('debug_mode', False), private_mode=False)
            else:
                # 如果不显示本地窗口，在单独线程中运行asyncio循环
                self.logger.info("本地预览窗口已禁用，仅运行后台服务。")

                def run_async_loop():
                    try:
                        # 在线程中创建新的事件循环
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        main_task = loop.create_task(self._main_async_loop())
                        loop.run_until_complete(main_task)
                    except Exception as e:
                        self.logger.error(f"异步循环运行出错: {e}")

                loop_thread = threading.Thread(target=run_async_loop, daemon=True)
                loop_thread.start()

                # 保持主线程运行
                try:
                    while not self.should_close:
                        time.sleep(1)
                except KeyboardInterrupt:
                    self.logger.info("收到中断信号，正在退出...")
                    self.should_close = True

        except Exception as e:
            self.logger.error(f"启动webview或后台服务时发生错误: {e}", exc_info=True)
        finally:
            if self.main_task and not self.main_task.done():
                self.main_task.cancel()
            if self.tcp_server:
                self.tcp_server.is_running = False
            self.logger.info("Web显示进程已退出")

    def _generate_queue_styles(self, queue_cfg: dict, queue_reg_family: str, queue_emo_family: str) -> str:
        """生成队列显示的样式"""
        queue_font_size = queue_cfg.get('font_size', '18px')
        headers_cfg = queue_cfg.get('headers', {})  # 新增：获取表头配置
        try:
            font_size_numeric = int(re.sub(r'[^\d]', '', queue_font_size))
            footer_font_size = int(font_size_numeric * 1.0)  #这里1.0控制了text_after字体的大小相对于上面字体比例
            # 新增：计算头部文本的字体大小
            header_font_size_multiplier = headers_cfg.get('text_before_font_size_multiplier', 1.0)
            header_font_size = int(font_size_numeric * header_font_size_multiplier)
        except ValueError:
            footer_font_size = 14
            header_font_size = int(18 * headers_cfg.get('text_before_font_size_multiplier', 1.0))  # Fallback

        # 新增：获取头部文本的左边距
        header_margin_left = headers_cfg.get('text_before_margin_left_px', 0)

        # 新增：处理队列位置
        queue_pos = queue_cfg.get('position', {})
        top = queue_pos.get('top', queue_cfg.get('position_y', '50px')) # 兼容旧配置
        left = queue_pos.get('left', queue_cfg.get('position_x', '20px')) # 兼容旧配置

        # 注意：付费玩家样式现在由JavaScript动态应用，不再生成静态CSS

        return f"""
        #queueDisplayContainer {{
            position: absolute;
            top: {top};
            left: {left};
            z-index: {queue_cfg.get('z_index', 10)};
            border-radius: {queue_cfg.get('border_radius', '8px')};
            padding: {queue_cfg.get('padding', '10px')};
            font-family: {queue_reg_family};
            font-size: {queue_font_size};
            color: {queue_cfg.get('font_color', '#ffffff')};
            /* 使用 text-shadow 替代 text-stroke 以提高OBS兼容性 */
            text-shadow:
                -{queue_cfg.get('stroke_width', '2px')} -{queue_cfg.get('stroke_width', '2px')} {queue_cfg.get('shadow_blur', '0px')} {queue_cfg.get('stroke_color', '#000000')},
                -{queue_cfg.get('stroke_width', '2px')}  0px {queue_cfg.get('shadow_blur', '0px')} {queue_cfg.get('stroke_color', '#000000')},
                -{queue_cfg.get('stroke_width', '2px')}  {queue_cfg.get('stroke_width', '2px')} {queue_cfg.get('shadow_blur', '0px')} {queue_cfg.get('stroke_color', '#000000')},
                 0px -{queue_cfg.get('stroke_width', '2px')} {queue_cfg.get('shadow_blur', '0px')} {queue_cfg.get('stroke_color', '#000000')},
                 0px  {queue_cfg.get('stroke_width', '2px')} {queue_cfg.get('shadow_blur', '0px')} {queue_cfg.get('stroke_color', '#000000')},
                 {queue_cfg.get('stroke_width', '2px')} -{queue_cfg.get('stroke_width', '2px')} {queue_cfg.get('shadow_blur', '0px')} {queue_cfg.get('stroke_color', '#000000')},
                 {queue_cfg.get('stroke_width', '2px')}  0px {queue_cfg.get('shadow_blur', '0px')} {queue_cfg.get('stroke_color', '#000000')},
                 {queue_cfg.get('stroke_width', '2px')}  {queue_cfg.get('stroke_width', '2px')} {queue_cfg.get('shadow_blur', '0px')} {queue_cfg.get('stroke_color', '#000000')};
        }}
        .queue-header {{
            text-align: left; /* 修改为左对齐 */
            margin-bottom: 8px;
            font-weight: bold;
            font-size: {header_font_size}px; /* 新增：应用计算后的字体大小 */
            margin-left: {header_margin_left}px; /* 新增：应用左边距 */
        }}
        .queue-table {{
            width: auto; /* 改为自动宽度，由列宽度决定 */
            border-spacing: 0 {queue_cfg.get('table_spacing', '8px')};
            table-layout: fixed; /* 固定表格布局 */
        }}
        .queue-table th, .queue-table td {{
            text-align: left;
            vertical-align: middle;
            padding: 2px 4px;
            overflow: hidden; /* 防止内容溢出 */
            white-space: nowrap; /* 防止换行 */
        }}
        .queue-table th {{ font-weight: bold; border-bottom: 1px solid {queue_cfg.get('font_color', '#ffffff')}; padding-bottom: 4px; }}
        .col-index {{ width: {queue_cfg.get('column_widths', {}).get('index', '40px')}; text-align: center; }}
        .col-name {{ width: {queue_cfg.get('column_widths', {}).get('name', '180px')}; }}
        .col-comments {{ width: {queue_cfg.get('column_widths', {}).get('comments', '80px')}; text-align: center; }}
        .col-time {{ width: {queue_cfg.get('column_widths', {}).get('time', '60px')}; text-align: center; }}
        .queue-footer {{ text-align: center; margin-top: 8px; font-size: {footer_font_size}px; line-height: 1.2; }}
        .queue-name-regular {{
            font-family: {queue_reg_family}, {queue_emo_family};
            display: inline-block;
        }}
        .queue-name-emoji {{
            font-family: {queue_emo_family}, {queue_reg_family};
            display: inline-block;
        }}
        """

class WebTextDisplay:
    def __init__(self, config: Dict[str, Any], stop_flag: Optional[Dict[str, bool]] = None):
        self.logger = get_logger('WebTextDisplayClient')
        self.main_config = config
        self.web_display_config = self.main_config.get('web_display', {})
        self.stop_flag = stop_flag
        
        self.tcp_port = self.web_display_config.get('websocket_port', 5558)
        
        self.process: Optional[Process] = None
        self.is_initialized = False
        
        self.client_socket: Optional[socket.socket] = None
        self.is_connected_to_server = False
        self.socket_lock = threading.RLock()

        if not self.web_display_config.get('enabled', False):
            self.logger.info("Web显示功能在配置中未启用 (WebTextDisplayClient init)")

    def _connect_to_server(self) -> bool:
        """连接到Web显示TCP服务器，带重试机制"""
        with self.socket_lock:
            if self.is_connected_to_server and self.client_socket:
                return True

            connect_retries = self.web_display_config.get('connect_retries', 5)
            connect_timeout = self.web_display_config.get('connect_timeout_seconds', 1.0)
            retry_delay = self.web_display_config.get('connect_retry_delay_seconds', 0.5)

            for attempt in range(connect_retries):
                if self.stop_flag and not self.stop_flag.get('running', True):
                    self.logger.info("停止标志已设置，中止连接尝试。")
                    return False
                try:
                    self.client_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    self.client_socket.settimeout(connect_timeout)
                    self.client_socket.connect(('localhost', self.tcp_port))
                    self.is_connected_to_server = True
                    self.logger.info(f"成功连接到Web显示服务器，端口: {self.tcp_port}")
                    return True
                except socket.timeout:
                    self.logger.warning(f"连接到Web显示服务器超时 (尝试 {attempt + 1}/{connect_retries})")
                except (socket.error, ConnectionRefusedError) as e:
                    self.logger.warning(f"连接到Web显示服务器失败 (尝试 {attempt + 1}/{connect_retries}): {e}")
                
                if self.client_socket:
                    self.client_socket.close()
                    self.client_socket = None
                
                if attempt < connect_retries - 1:
                    time.sleep(retry_delay * (attempt + 1))
            
            self.logger.error("多次尝试后未能连接到Web显示服务器。")
            self.is_connected_to_server = False
            return False

    def _send_command(self, command: dict) -> bool:
        """发送命令到Web显示服务器"""
        if not self.is_initialized:
            self.logger.warning("WebTextDisplay尚未初始化成功，无法发送命令。")
            return False

        with self.socket_lock:
            if not self.is_connected_to_server:
                self.logger.warning("未连接到服务器，尝试重新连接...")
                if not self._connect_to_server():
                    self.logger.error("重新连接失败，无法发送命令。")
                    return False
            
            if not self.client_socket:
                 self.logger.error("客户端套接字无效，无法发送命令。")
                 return False

            try:
                command_json = json.dumps(command, ensure_ascii=False)
                self.client_socket.sendall((command_json + '\n').encode('utf-8'))
                self.logger.debug(f"发送命令: {command}")
                return True
            except (socket.error, BrokenPipeError, ConnectionResetError) as e:
                self.logger.error(f"发送命令时出错: {e}。标记为未连接。")
                self.is_connected_to_server = False
                if self.client_socket:
                    try:
                        self.client_socket.close()
                    except socket.error: pass
                self.client_socket = None
                return False
            except Exception as e:
                self.logger.error(f"发送命令时发生意外错误: {e}", exc_info=True)
                return False

    def start(self) -> bool:
        """启动独立的web显示子进程并连接到其TCP服务器。"""
        if not self.web_display_config.get('enabled', False):
            self.logger.info("WebTextDisplay 在配置中未启用，不启动。")
            return False

        if self.is_initialized and self.process and self.process.is_alive():
            self.logger.warning("Web显示进程已在运行中。")
            if not self.is_connected_to_server:
                return self._connect_to_server()
            return True
        
        try:
            self.process = Process(target=run_web_display_standalone_process_target, args=(self.main_config,), daemon=True)
            self.process.start()
            self.logger.info(f"已启动Web显示独立进程，PID: {self.process.pid if self.process.pid else 'Unknown'}")

            time.sleep(self.web_display_config.get('start_delay_seconds', 2.0))

            if self._connect_to_server():
                self.is_initialized = True
                self.logger.info("WebTextDisplay 成功启动并连接到子进程。")
                return True
            else:
                self.logger.error("Web显示进程已启动，但未能连接到其TCP服务器。尝试清理...")
                if self.process and self.process.is_alive():
                    self.process.terminate()
                    self.process.join(timeout=1.0)
                self.process = None
                self.is_initialized = False
                return False
        except Exception as e:
            self.logger.error(f"启动Web显示子进程时发生严重错误: {e}", exc_info=True)
            self.is_initialized = False
            if self.process and self.process.is_alive():
                self.process.terminate()
            self.process = None
            return False

    def destroy(self):
        """停止Web显示子进程并清理资源"""
        self.logger.info("正在销毁 WebTextDisplay...")
        
        if self.is_initialized and self.is_connected_to_server:
            self.logger.info("发送退出命令到Web显示进程...")
            self._send_command({'action': 'exit'})
            time.sleep(0.2)

        with self.socket_lock:
            if self.client_socket:
                try:
                    self.client_socket.shutdown(socket.SHUT_RDWR)
                except (socket.error, OSError):
                    pass
                try:
                    self.client_socket.close()
                except socket.error:
                    pass
                self.client_socket = None
            self.is_connected_to_server = False

        if self.process:
            if self.process.is_alive():
                self.logger.info(f"正在终止 WebDisplayProcess (PID: {self.process.pid if self.process.pid else 'Unknown'})...")
                self.process.terminate()
                self.process.join(timeout=self.web_display_config.get('terminate_timeout_seconds', 3.0))
                if self.process.is_alive():
                    self.logger.warning(f"WebDisplayProcess (PID: {self.process.pid if self.process.pid else 'Unknown'}) 未能优雅终止，正在强制终止 (kill)。")
                    self.process.kill()
                    self.process.join(timeout=2.0)
            else:
                self.logger.info(f"WebDisplayProcess (PID: {self.process.pid if self.process.pid else 'Unknown'}) 先前已终止或结束。")
            self.process = None
        
        self.is_initialized = False
        self.logger.info("WebTextDisplay 已销毁。")

    # Public API methods
    def show_message(self, text: str, duration: int = 0, animated: bool = False):
        """显示消息"""
        # duration 现在直接传递给JS，可以是0或者null表示不自动清除，或者具体秒数
        self._send_command({
            'action': 'show_message',
            'text': text,
            'duration': duration if duration > 0 else None,  # 如果duration是0或负数，发送null
            'animated': animated
        })

    def show_status_message(self, status_type: str, player_name: str, item_name: str = "", success_count: Optional[int] = None):
        """显示状态消息"""
        # duration 会在服务器端从配置中读取并传递给JS
        self._send_command({
            'action': 'show_status_message',
            'status_type': status_type,
            'player_name': player_name,
            'item_name': item_name,
            'success_count': success_count
        })

    def update_queue(self, queue_data: List[Dict[str, Any]]):
        """更新队列数据"""
        self._send_command({
            'action': 'update_queue',
            'data': queue_data
        })

    def clear_text(self):
        """清除显示的文本"""
        self._send_command({
            'action': 'clear_text'
        })

    def exit(self):
        """退出Web显示"""
        self._send_command({
            'action': 'exit'
        })

    def update_detection_display(self, objects_data: List[Dict[str, Any]]):
        """更新检测到的物体显示 (主要用于触发抓取区域可视化更新)"""
        self._send_command({
            'action': 'update_detection_display',
            'data': objects_data
        })

    # --- 以下三方法是为了兼容旧版调用 ---
    def show_playing_status(self, player_name: str):
        """显示玩家正在游戏中的状态"""
        # status_type 对应 play_main 里 "game_start" 发送的 'playing'
        self.show_status_message('playing', player_name)

    def show_failure_message(self, player_name: str):
        """显示玩家未抓中时的提示"""
        # status_type 对应 handle_status_updates_worker 里的 caught_nothing
        self.show_status_message('caught_nothing', player_name)

    def show_success_message(self, player_name: str, item_name: str, success_count: Optional[int] = None):
        """显示玩家抓中时的祝贺"""
        # status_type 对应 handle_status_updates_worker 里的 caught_item
        self.show_status_message('caught_item', player_name, item_name, success_count)

    def update_config(self, new_web_display_config: Dict[str, Any]):
        """更新Web显示的配置参数（线程安全）"""
        with self.socket_lock:
            try:
                # 仅发送有变化的配置，以减少数据量和避免不必要的更新
                changed_config = {}
                current_config = self.web_display_config

                for key, new_value in new_web_display_config.items():
                    # 使用json.dumps进行深比较，确保嵌套对象也能正确判断变化
                    if key not in current_config or json.dumps(current_config[key], sort_keys=True) != json.dumps(new_value, sort_keys=True):
                        changed_config[key] = new_value

                if not changed_config:
                    self.logger.debug("Web显示配置无实际变化，不发送更新。")
                    return True

                # 更新本地持有的配置
                self.web_display_config.update(new_web_display_config)
                self.main_config['web_display'] = self.web_display_config

                self.logger.info(f"Web显示配置已更新，变化的键: {list(changed_config.keys())}")

                # 发送包含所有变化的配置更新命令
                # 将变化的配置包裹在 'web_display' 键下，方便前端统一处理
                self._send_command({
                    'action': 'update_config',
                    'config': { 'web_display': changed_config }
                })
                return True

            except Exception as e:
                self.logger.error(f"更新Web显示配置时出错: {e}", exc_info=True)
                return False

def run_web_display_standalone_process_target(config_dict: Dict[str, Any]):
    process_target_logger = get_logger('WebDisplayProcessTarget')
    process_target_logger.info("Web显示子进程目标函数已启动")
    
    try:
        display_process_instance = WebDisplayProcess(config_dict)
        if display_process_instance.config.get('enabled', False):
            display_process_instance.run()
        else:
            process_target_logger.info("Web显示在配置中未启用")
    except Exception as e:
        process_target_logger.error(f"Web显示子进程发生错误: {e}", exc_info=True)
    finally:
        process_target_logger.info("Web显示子进程已结束")

if __name__ == "__main__":
    # 示例：直接在主程序中运行Web显示功能
    config_example = {
        "web_display": {
            "enabled": True,
            "websocket_port": 5558,
            "window_title": "文本显示",
            "background_color": "rgba(0,0,0,0)",
            "on_top": False,
            "frameless": False,
            "display_settings": {
                "playing": {
                    "font_size": "32px",
                    "font_color": "#00FF00",
                    "stroke_width": "6px",
                    "stroke_color": "#000000",
                    "z_index": 20
                },
                "caught_nothing": {
                    "font_size": "28px",
                    "font_color": "#FFFF00",
                    "stroke_width": "4px",
                    "stroke_color": "#333333",
                    "z_index": 21
                },
                "caught_item": {
                    "font_size": "36px",
                    "font_color": "#FF69B4",
                    "stroke_width": "8px",
                    "stroke_color": "#333333",
                    "z_index": 22,
                    "pulse_duration_seconds": 0.3,
                    "pulse_scale_from": 0.9,
                    "pulse_scale_to": 1.1,
                    "pulse_opacity_from": 1.0,
                    "pulse_opacity_to": 1.0
                }
            },
            "queue_display": {
                "enabled": True,
                "position_x": "20px",
                "position_y": "50px",
                "z_index": 10,
                "border_radius": "8px",
                "padding": "10px",
                "font_color": "#ffffff",
                "stroke_width": "2px",
                "stroke_color": "#000000",
                "table_spacing": "8px",
                "column_widths": {
                    "index": "40px",
                    "name": "180px",
                    "comments": "80px",
                    "time": "60px"
                },
                "paid_player_color": "#FFD700"
            }
        }
    }

    # 直接运行Web显示进程
    display_process = WebDisplayProcess(config_example)
    display_process.run()
