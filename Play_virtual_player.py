import logging
import threading
import time
import random
import math
import copy
import queue
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import play_processing  # 用于调用 calculate_priority

logger = logging.getLogger(__name__)

class VirtualPlayer:
    """代表一个虚拟玩家及其状态"""
    def __init__(self, player_id: str, name: str):
        self.player_id = player_id
        self.name = name
        self.games_played = 0
        self.is_in_queue = False
        self.is_retired = False
        self.is_paid_status = False
        self.simulated_come_time = time.time()
        # --- 新增：提拔状态 ---
        self.is_promoted = False
        # --- 修改结束 ---
        self.base_comments = 0.0  # 入队时的基础评论数
        self.comment_growth_rate_per_min = 0.0 # 入队时计算出的每分钟评论增长率

    def __repr__(self):
        return (f"VirtualPlayer(id={self.player_id}, name={self.name}, games={self.games_played}, "
                f"in_queue={self.is_in_queue}, retired={self.is_retired}, promoted={self.is_promoted})")

class VirtualPlayerManager:
    """管理虚拟玩家的生命周期、行为和插入逻辑"""
    def __init__(self, config: Dict[str, Any], stop_flag: Dict[str, bool],
                 player_info: Dict, player_comments: Dict, player_games: Dict, in_que: List,
                 player_info_lock: threading.RLock, player_comments_lock: threading.RLock,
                 player_games_lock: threading.RLock, queue_lock: threading.Lock,
                 status_update_queue: queue.Queue):
        self.vp_config = config.get('virtual_player', {})
        # 修正：确保game_config在初始化时就获取
        self.game_config = config.get('game', {})
        self.stop_flag = stop_flag

        self.player_info = player_info
        self.player_comments = player_comments
        self.player_games = player_games
        self.in_que = in_que
        self.player_info_lock = player_info_lock
        self.player_comments_lock = player_comments_lock
        self.player_games_lock = player_games_lock
        self.queue_lock = queue_lock
        self.status_update_queue = status_update_queue

        self.active_pool: Dict[str, VirtualPlayer] = {}
        self.player_list: List[Tuple[str, str]] = []
        self.current_list_index = 0
        # --- 新增：真实玩家游戏计数器 ---
        self.real_games_since_last_promotion = 0
        # --- 修改结束 ---

        self.thread = threading.Thread(target=self._manager_worker, daemon=True, name="VirtualPlayerManagerThread")

    def start(self):
        if not self.vp_config.get('enabled', False):
            logger.info("[虚拟玩家] 功能未启用，管理器未启动。")
            return

        if not self._load_player_list():
            logger.error("[虚拟玩家] 玩家列表加载失败，管理器未启动。")
            return

        logger.info("[虚拟玩家] 管理器启动...")
        self.thread.start()

    def _load_player_list(self) -> bool:
        filename = self.vp_config.get('list_filename')
        if not filename:
            logger.error("[虚拟玩家] 未在配置中找到 list_filename")
            return False
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                for line in f:
                    parts = line.strip().split()
                    if len(parts) >= 2 and "VirtualPlayer" in parts[1]:
                        self.player_list.append((parts[1], parts[0]))  # (id, name)
            if not self.player_list:
                logger.error(f"[虚拟玩家] 玩家列表文件 {filename} 为空或格式不正确（每行应为：昵称 VirtualPlayerID...）。")
                return False

            self.current_list_index = random.randint(0, len(self.player_list) - 1)
            logger.info(f"[虚拟玩家] 成功加载 {len(self.player_list)} 个虚拟玩家，起始索引: {self.current_list_index}")
            return True
        except FileNotFoundError:
            logger.error(f"[虚拟玩家] 找不到玩家列表文件: {filename}")
            return False
        except Exception as e:
            logger.error(f"[虚拟玩家] 加载玩家列表时出错: {e}")
            return False

    def _get_new_player_from_list(self) -> Optional[VirtualPlayer]:
        if not self.player_list:
            return None

        for _ in range(len(self.player_list)):
            player_id, name = self.player_list[self.current_list_index]
            self.current_list_index = (self.current_list_index + 1) % len(self.player_list)
            if player_id not in self.active_pool:
                return VirtualPlayer(player_id, name)

        logger.warning("[虚拟玩家] 玩家列表已全部在活跃池中，无法获取新玩家。")
        return None

    def _manager_worker(self):
        check_interval = self.vp_config.get('check_interval_seconds', 15)
        while self.stop_flag.get('running', True):
            time.sleep(check_interval)
            try:
                self._update_virtual_player_states()
                self._check_and_add_players()
            except Exception as e:
                logger.error(f"[虚拟玩家] 管理器循环出错: {e}", exc_info=True)
        logger.info("[虚拟玩家] 管理器线程已停止。")

    def check_and_inject_player_on_demand(self, already_has_lock=False):
        """
        按需检查并注入虚拟玩家。此方法应在真实玩家成功加入队列后被外部调用。
        Args:
            already_has_lock (bool): 如果调用者已经持有queue_lock，设为True以避免死锁。
        """
        try:
            if already_has_lock:
                self._check_and_inject_consecutive_players()
            else:
                with self.queue_lock:
                    self._check_and_inject_consecutive_players()
        except Exception as e:
            logger.error(f"[虚拟玩家-按需] 按需检查时出错: {e}", exc_info=True)

    def increment_real_game_count(self):
        """
        当一个真实玩家完成游戏时，由外部调用此方法来增加计数器，
        并根据条件触发虚拟玩家的提拔。
        """
        with self.queue_lock:
            # 检查是否处于填充模式
            real_players_in_queue = [p for p in self.in_que if "VirtualPlayer" not in p[0][3]]
            if len(real_players_in_queue) > self.vp_config.get('min_real_players_in_queue', 2):
                # 如果不是填充模式，则不进行任何操作
                return

            self.real_games_since_last_promotion += 1
            logger.debug(f"[虚拟玩家-提拔] 真实玩家游戏计数: {self.real_games_since_last_promotion}")

            promotion_trigger_count = self.vp_config.get('stage_for_real_players', 7) - 3
            if self.real_games_since_last_promotion >= promotion_trigger_count:
                self._promote_a_player_unlocked()
                self.real_games_since_last_promotion = 0 # 重置计数器

    def _promote_a_player_unlocked(self):
        """
        不带锁的提拔玩家逻辑。选择队列中最靠前的普通虚拟玩家并标记为 is_promoted。
        此方法期望在已持有 queue_lock 的上下文中被调用。
        """
        # 检查是否已有被提拔的玩家
        # 修复类型检查错误：先获取玩家对象，确认它是一个 VirtualPlayer 实例，然后再访问其属性
        if any(
            "VirtualPlayer" in item[0][3] 
            and (vp := self.active_pool.get(item[0][3])) is not None 
            and vp.is_promoted 
            for item in self.in_que
        ):
            logger.debug("[虚拟玩家-提拔] 队列中已有被提拔的玩家，本次不提拔。")
            return

        # 从队首开始查找，提拔最靠前的虚拟玩家
        for item in self.in_que:
            p_id = item[0][3]
            if "VirtualPlayer" in p_id:
                vp = self.active_pool.get(p_id)
                if vp and not vp.is_promoted:
                    vp.is_promoted = True
                    logger.info(f"[虚拟玩家-提拔] 已提拔玩家 {vp.name}。他将在后续排序中逐步前提。")
                    return
        
        logger.debug("[虚拟玩家-提拔] 队列中没有可供提拔的虚拟玩家。")

    def _check_and_inject_consecutive_players(self):
        """
        检查连续真实玩家并在需要时插入虚拟玩家的核心逻辑。
        此方法期望在已持有 queue_lock 的上下文中被调用。
        """
        if not self.in_que:
            return

        stage_size = self.vp_config.get('stage_for_real_players', 7)
        consecutive_real_players = 0

        # 从队列末尾向前检查，因为新玩家刚被加入到末尾
        for item in reversed(self.in_que):
            is_virtual = "VirtualPlayer" in item[0][3]
            if not is_virtual:
                consecutive_real_players += 1
            else:
                # 遇到第一个虚拟玩家，说明连续中断，停止计数
                break
        
        # 如果连续真实玩家数量达到或超过阈值，则插入一个虚拟玩家
        if consecutive_real_players >= stage_size:
            logger.info(f"[虚拟玩家-按需] 检测到 {consecutive_real_players} 个连续真实玩家 (阈值: {stage_size})，准备插入虚拟玩家。")
            # 使用不带锁的方法添加虚拟玩家
            self._add_virtual_player_to_queue_unlocked()

    def _update_virtual_player_states(self):
        with self.player_info_lock, self.player_games_lock, self.queue_lock:
            queued_ids = {item[0][3] for item in self.in_que}
            for vp in list(self.active_pool.values()):
                if vp.is_in_queue and vp.player_id not in queued_ids:
                    vp.is_in_queue = False
                    vp.games_played += 1
                    # --- 新增：游戏结束后重置提拔状态 ---
                    if vp.is_promoted:
                        vp.is_promoted = False
                        logger.info(f"[虚拟玩家-提拔] 被提拔的玩家 {vp.name} 已完成游戏，重置状态。")
                    # --- 修改结束 ---
                    logger.info(f"[虚拟玩家] {vp.name} 已完成游戏，总游戏次数: {vp.games_played}")

                    if vp.games_played >= self.vp_config.get('max_games_per_player', 4):
                        vp.is_retired = True
                        logger.info(f"[虚拟玩家] {vp.name} 已退休。")
                        del self.active_pool[vp.player_id]
                        new_vp = self._get_new_player_from_list()
                        if new_vp:
                            self.active_pool[new_vp.player_id] = new_vp
                            logger.info(f"[虚拟玩家] 新玩家 {new_vp.name} 已补充到活跃池。")

                max_free = self.game_config.get('max_free_games_per_session', 1)
                if not vp.is_paid_status and vp.games_played >= max_free:
                    vp.is_paid_status = True
                    logger.info(f"[虚拟玩家] {vp.name} 下次将以付费状态排队。")

    def _check_and_add_players(self):
        """
        定时检查器，主要负责在队列人数较少时进行"填充"。
        分段插入逻辑已移至 check_and_inject_player_on_demand。
        """
        with self.queue_lock:
            real_players_in_queue = [p for p in self.in_que if "VirtualPlayer" not in p[0][3]]
            num_real = len(real_players_in_queue)
            num_total = len(self.in_que)

        min_real = self.vp_config.get('min_real_players_in_queue', 2)
        stage_size = self.vp_config.get('stage_for_real_players', 7)

        # 填充模式：当真实玩家很少时，填充一些虚拟玩家以维持队列热度
        if num_real <= min_real:
            # 目标总人数随机在 (min_real, stage_size) 之间
            target_total = random.randint(min_real + 1, stage_size)
            if num_total < target_total:
                logger.info(f"[虚拟玩家-填充] 真实玩家数({num_real}) <= 阈值({min_real})，且总数({num_total}) < 目标({target_total})，添加虚拟玩家。")
                self._add_virtual_player_to_queue()

    def _add_virtual_player_to_queue(self):
        """带锁版本的添加玩家到队列方法，供定时器等外部调用"""
        with self.queue_lock:
            self._add_virtual_player_to_queue_unlocked()

    def _add_virtual_player_to_queue_unlocked(self):
        """
        不带锁的核心实现，将一个可用的虚拟玩家添加到队列。
        此方法期望在已持有 queue_lock 的上下文中被调用。
        """
        pool_size = self.vp_config.get('active_pool_size', 5)
        while len(self.active_pool) < pool_size:
            new_vp = self._get_new_player_from_list()
            if new_vp:
                self.active_pool[new_vp.player_id] = new_vp
            else:
                break # 如果列表已用完，则停止补充

        # --- 核心修改：优先选择游戏次数最少的虚拟玩家 ---
        # 1. 筛选出所有可用的玩家
        available_players = [vp for vp in self.active_pool.values() if not vp.is_in_queue and not vp.is_retired]

        # 2. 按游戏次数升序排序，选择次数最少的
        if available_players:
            available_players.sort(key=lambda vp: vp.games_played)
            candidate = available_players[0]
            logger.debug(f"[虚拟玩家] 候选玩家池: {[f'{p.name}({p.games_played}次)' for p in available_players]}。已选择: {candidate.name}")
        else:
            candidate = None
        # --- 修改结束 ---

        if not candidate:
            # 如果池中没有可用玩家，尝试从列表中获取一个新玩家
            candidate = self._get_new_player_from_list()
            if candidate:
                self.active_pool[candidate.player_id] = candidate
            else:
                logger.warning("[虚拟玩家] 无法找到任何可用的虚拟玩家来添加到队列。")
                return

        self._prepare_and_queue_player_unlocked(candidate)
    
    def _prepare_and_queue_player(self, vp: VirtualPlayer):
        """准备并排队一个虚拟玩家（带锁版本）"""
        with self.player_info_lock, self.player_comments_lock, self.player_games_lock, self.queue_lock:
            self._prepare_and_queue_player_unlocked(vp)

    def _prepare_and_queue_player_unlocked(self, vp: VirtualPlayer):
        """
        不带锁的准备和排队玩家的核心逻辑。
        此方法期望在已持有 queue_lock 的上下文中被调用。
        """
        with self.player_info_lock, self.player_comments_lock, self.player_games_lock:
            # --- 核心修改：现在此方法返回三个值 ---
            avg_stay, avg_comments, avg_comment_rate = self._calculate_real_player_averages_unlocked()

            # 只有当玩家不在队列中时（即一次新的排队），才刷新这些值
            if not vp.is_in_queue:
                vp.simulated_come_time = time.time() - avg_stay * 60
                vp.base_comments = float(avg_comments)
                vp.comment_growth_rate_per_min = float(avg_comment_rate)

            # 初始的评论数
            initial_comments = int(vp.base_comments)

            p_info_data = {
                'plat': 'virtual', 'name': vp.name, 'head_img': '',
                'come_time': time.strftime("%a %b %d %H:%M:%S %Y", time.localtime(vp.simulated_come_time)),
                'comments_after_game': initial_comments, # 存储初始值
                'free_games_used_this_session': vp.games_played,
                'player_id': vp.player_id
            }
            self.player_info[vp.player_id] = p_info_data

            target_id = self.vp_config.get('default_target_id', "1")
            entry = [p_info_data['plat'], p_info_data['come_time'], vp.name, vp.player_id, target_id, '', 'virtual_target']
            order_id = "virtual_paid" if vp.is_paid_status else None
            
            config_for_priority = {'game': self.game_config}
            # 优先级仍然需要计算，以备不时之需或用于显示，但它不再决定最终位置
            priority = play_processing.calculate_priority(vp.player_id, entry, self.player_info, self.player_comments, self.player_games, config_for_priority, order_id=order_id)

            # --- 核心修改：移除排序，只将虚拟玩家添加到队尾 ---
            self.in_que.append((entry, 1.0, priority, order_id))
            # self.in_que.sort(key=lambda x: (-x[2], x[0][1])) # <--- 删除此行

            vp.is_in_queue = True
            logger.info(f"[虚拟玩家] {vp.name} 已加入队列末尾。优先级: {priority:.2f} (此优先级仅供参考，位置由注入逻辑决定)")

            # 注意：这里的队列更新事件发送的是一个尚未最终排序的队列快照，
            # 但很快会被 process_batch_game_requests 中的排序覆盖，这是可接受的。
            self.status_update_queue.put({'type': 'queue_update', 'in_que': copy.deepcopy(self.in_que), 'player_info': copy.deepcopy(self.player_info)})

    def _calculate_real_player_averages(self) -> Tuple[float, float, float]:
        """带锁版本的计算真实玩家平均值"""
        with self.queue_lock:
            return self._calculate_real_player_averages_unlocked()

    def _calculate_real_player_averages_unlocked(self) -> Tuple[float, float, float]:
        """
        不带锁的计算真实玩家平均停留时间、评论数和评论增长率。
        此方法期望在已持有 queue_lock 的上下文中被调用。
        返回: (平均停留分钟, 平均评论数, 平均每分钟评论数)
        """
        real_players = [p for p in self.in_que if "VirtualPlayer" not in p[0][3]]
        if not real_players:
            return 1.0, 1.0, 0.1 # 如果没有真实玩家，返回默认值

        total_stay_min, total_comments, count = 0.0, 0.0, 0
        sample_size = self.vp_config.get('stage_for_real_players', 7)
        for item in real_players[:sample_size]:
            p_id = item[0][3]
            if p_id in self.player_info:
                p_info = self.player_info[p_id]
                try:
                    come_time = datetime.strptime(p_info['come_time'], "%a %b %d %H:%M:%S %Y")
                    stay_duration_min = (datetime.now() - come_time).total_seconds() / 60
                    
                    total_stay_min += stay_duration_min
                    total_comments += p_info.get('comments_after_game', 0)
                    count += 1
                except Exception:
                    continue
        
        if count == 0:
            return 1.0, 1.0, 0.1

        avg_stay = total_stay_min / count
        avg_comments = total_comments / count
        # 计算平均增长率，如果总停留时间过短，则使用一个小的默认值避免除零或过大
        avg_rate = (total_comments / total_stay_min) if total_stay_min > 1 else 0.1

        return avg_stay, avg_comments, avg_rate