import logging
import threading
from typing import Dict, List, Any, Optional

import Play_receiveMSG
import Play_db
import play_db_sync
from Play_request_guard import stop_health_check

logger = logging.getLogger(__name__)

def perform_cleanup(
    stop_flag: Dict[str, bool],
    obs_controller: Any,
    db_sync_manager: Any,
    queue_lock: threading.Lock,
    in_que: List,
    game_thread: Optional[threading.Thread] = None,
    monitor_thread: Optional[threading.Thread] = None,
    health_stopped: bool = False,
    gui_thread: Optional[threading.Thread] = None,  # 保留兼容性，但不再使用
    order_fetching_thread: Optional[threading.Thread] = None,  # 添加订单抓取线程参数
    detection_receiver = None,  # 添加检测接收器参数
    move_service_client = None,  # 移动服务客户端参数
    web_display = None,  # 添加Web显示参数
    virtual_player_manager_thread: Optional[threading.Thread] = None  # 添加虚拟玩家管理器线程参数
) -> bool:
    """
    执行程序退出时的清理操作
    
    参数:
    - stop_flag: 停止标志字典
    - obs_controller: OBS控制器实例
    - db_sync_manager: 数据库同步管理器实例
    - queue_lock: 队列锁
    - in_que: 游戏请求队列
    - game_thread: 游戏处理线程
    - monitor_thread: 监控线程
    - health_stopped: 健康检查线程是否已停止
    - gui_thread: GUI线程（已弃用，保留兼容性）
    - order_fetching_thread: 订单抓取线程
    - detection_receiver: 检测数据接收器实例
    - move_service_client: 移动服务客户端实例
    - web_display: Web显示实例
    
    返回:
    - bool: 清理是否成功完成
    """
    logger.info("[清理] 开始执行清理操作")
    stop_flag['running'] = False
    logger.debug("[清理] 设置 stop_flag['running'] = False")

    # 停止消息获取线程
    success = True
    try:
        logger.debug("[清理] 停止消息获取线程")
        Play_receiveMSG.stop_message_thread()
    except Exception as e:
        logger.error(f"[清理] 停止消息获取线程失败: {e}")
        success = False

    # 0.1 停止Web显示进程
    if web_display:
        try:
            logger.debug("[清理] 停止Web显示进程...")
            web_display.destroy()
            logger.info("[清理] Web显示进程已关闭")
        except Exception as e:
            logger.error(f"[清理] 停止Web显示进程失败: {e}")
            success = False

    # 0.2 停止移动服务客户端
    if move_service_client:
        try:
            logger.debug("[清理] 停止移动服务客户端...")
            move_service_client.stop()
            logger.info("[清理] 移动服务客户端已停止")
        except Exception as e:
            logger.error(f"[清理] 停止移动服务客户端失败: {e}")
            success = False

    # 1. 停止检测数据接收器
    if detection_receiver:
        try:
            logger.debug("[清理] 断开检测服务器连接...")
            detection_receiver.disconnect()
            logger.info("[清理] 检测数据接收器已停止")
        except Exception as e:
            logger.error(f"[清理] 停止检测数据接收器失败: {e}")
            success = False

    # 2. 停止数据库同步线程
    if db_sync_manager:
        try:
            db_sync_manager.stop()
        except Exception as e:
            logger.error(f"[清理] 停止数据库同步管理器失败: {e}")
            success = False

    # 2. 强制隐藏 OBS 视频源
    if obs_controller:
        try:
            obs_controller._update_source_visibility(obs_controller.congrats_video_source, False)
            logger.debug("[清理] 强制隐藏 OBS 视频源")
        except Exception as e:
            logger.error(f"[清理] 强制隐藏 OBS 视频源失败: {e}")
            logger.error("[清理] OBS清理详细错误", exc_info=True)
            success = False

    # 3. 等待游戏线程结束
    if game_thread and game_thread.is_alive():
        try:
            logger.debug("[清理] 等待游戏线程结束…")
            game_thread.join(timeout=5)
            if game_thread.is_alive():
                logger.warning("[清理] 游戏线程未在5秒内结束")
            else:
                logger.info("[清理] 游戏线程已结束")
        except Exception as e:
            logger.error(f"[清理] 等待游戏线程结束时出错: {e}")
            success = False

    # 4. 等待主循环监控线程结束（若有）
    if monitor_thread and monitor_thread.is_alive():
        try:
            logger.debug("[清理] 等待监控线程结束…")
            monitor_thread.join(timeout=2)
            logger.info("[清理] 主循环监控线程已停止")
        except Exception as e:
            logger.error(f"[清理] 等待监控线程结束时出错: {e}")
            success = False
    
    # 4.1 GUI线程处理（已弃用，但保留兼容性）
    if gui_thread and gui_thread.is_alive():
        try:
            logger.debug("[清理] 等待GUI线程结束…（注意：GUI现在是独立进程）")
            gui_thread.join(timeout=1)  # 缩短超时时间
            if gui_thread.is_alive():
                logger.warning("[清理] GUI线程未在1秒内结束（正常，因为GUI已改为独立进程）")
            else:
                logger.info("[清理] GUI线程已结束")
        except Exception as e:
            logger.error(f"[清理] 等待GUI线程结束时出错: {e}")
            # 不设置success = False，因为GUI进程由主程序管理
            
    # 4.2 等待订单抓取线程结束（若有）
    if order_fetching_thread and order_fetching_thread.is_alive():
        try:
            logger.debug("[清理] 等待订单抓取线程结束…")
            order_fetching_thread.join(timeout=10)  # 增加超时时间，因为需要关闭浏览器
            if order_fetching_thread.is_alive():
                logger.warning("[清理] 订单抓取线程未在10秒内结束")
            else:
                logger.info("[清理] 订单抓取线程已结束")
        except Exception as e:
            logger.error(f"[清理] 等待订单抓取线程结束时出错: {e}")
            success = False

    # 4.3 等待虚拟玩家管理器线程结束（若有）
    if virtual_player_manager_thread and virtual_player_manager_thread.is_alive():
        try:
            logger.debug("[清理] 等待虚拟玩家管理器线程结束…")
            virtual_player_manager_thread.join(timeout=5)
            if virtual_player_manager_thread.is_alive():
                logger.warning("[清理] 虚拟玩家管理器线程未在5秒内结束")
            else:
                logger.info("[清理] 虚拟玩家管理器线程已结束")
        except Exception as e:
            logger.error(f"[清理] 等待虚拟玩家管理器线程结束时出错: {e}")
            success = False

    # 5. 健康检查线程只在这里停止一次
    if not health_stopped:
        try:
            stop_health_check()
            health_stopped = True
        except Exception as e:
            logger.error(f"[清理] 停止健康检查失败: {e}")
            success = False

    # 6. 最终同步并关闭会话
    session_id = Play_db.get_current_session_id()
    if session_id and db_sync_manager:
        lock_acquired = False
        try:
            lock_acquired = queue_lock.acquire(timeout=5)
            if lock_acquired:
                # 直接执行 final_queue_update，确保退出时把 in_que 写入数据库
                db_sync_manager._process_sync_task("final_queue_update", (session_id, in_que.copy()))
                # 再做一次全量同步，刷入其它遗漏的数据
                db_sync_manager._perform_full_sync()
            else:
                logger.warning("[清理] 获取队列锁超时(5秒)，跳过最终队列同步")
            Play_db.close_session(session_id)
        except Exception as e:
            logger.error(f"[清理] 最终同步/关闭会话失败: {e}", exc_info=True)
            success = False
        finally:
            if lock_acquired:
                queue_lock.release()

    logger.info(f"[清理] 清理操作{'成功' if success else '部分失败'}")
    return success
