import obsws_python as obs
import yaml
import time
import logging
import threading
import datetime
import traceback
from typing import Dict, List, Any, Tuple

logger = logging.getLogger('play_obs')

class OBSController:
    def __init__(self, config=None):
        """初始化OBS控制器"""
        self.config = config or self._get_default_config()
        self.client = None
        self.connected = False
        self.lock = threading.Lock()
        self.last_update_time = 0
        
        # 获取配置 - 只从obs配置中读取
        obs_config = self.config.get('obs', {})
        self.update_interval = obs_config.get('update_interval', 0.08)
        self.congrats_video_source = obs_config.get('congrats_video_source', '抓中特效')
        self.congrats_duration = obs_config.get('congrats_duration', 2)
        
        # 缓存设置
        self.source_settings = {}
        self.cached_scene_name = None
        self.cached_scene_item_ids: Dict[str, int] = {}
        self.visibility_states: Dict[str, bool] = {}
        
        self.connect_to_obs()
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'obs': {'host': 'localhost', 'port': 4455, 'password': '',
                   'congrats_video_source': '抓中特效'}
        }

    def connect_to_obs(self):
        """连接到OBS WebSocket"""
        obs_config = self.config.get('obs', {})
        host = obs_config.get('host', 'localhost')
        port = obs_config.get('port', 4455)
        password = obs_config.get('password', '')
        
        try:
            logger.info(f"正在连接到OBS WebSocket (host={host}, port={port})...")
            self.client = obs.ReqClient(host=host, port=port, password=password, timeout=5)
            self.connected = True
            logger.info("成功连接到OBS WebSocket")
            
            # 缓存场景信息
            try:
                response = self.client.get_current_program_scene()
                # 修正: 使用正确的属性访问方式
                current_scene = response.current_program_scene_name
                self.cached_scene_name = current_scene
                logger.info(f"当前场景: {current_scene}")
                
                # 修正: 使用正确的属性访问方式
                scene_items_response = self.client.get_scene_item_list(current_scene)
                scene_items = scene_items_response.scene_items
                sources_to_cache = [self.congrats_video_source]
                
                for name in sources_to_cache:
                    if not name:
                        continue
                    
                    for item in scene_items:
                        if item['sourceName'] == name:
                            self.cached_scene_item_ids[name] = item['sceneItemId']
                            self.visibility_states[name] = item['sceneItemEnabled']
                            break
                    
                    if name not in self.cached_scene_item_ids:
                        logger.warning(f"未在场景 '{current_scene}' 中找到源 '{name}'")
                
                logger.info(f"完成源缓存，已缓存 {len(self.cached_scene_item_ids)} 个源的场景项ID")
                
            except Exception as e:
                logger.warning(f"缓存源信息时出错: {e}")
        except Exception as e:
            logger.error(f"连接到OBS WebSocket失败: {e}")
            self.connected = False
            self._clear_cache()
    
    def _clear_cache(self):
        """清除所有缓存"""
        self.cached_scene_name = None
        self.cached_scene_item_ids.clear()
        self.visibility_states.clear()
        self.source_settings.clear()
    
    def update_queue_display(self, in_que: List[Tuple], player_info: Dict):
        """更新OBS中的队列显示 - 此方法不再需要更新文本"""
        logger.debug("OBSController.update_queue_display called, but OBS text display is disabled.")
        return True
    
    def show_quelist_source(self, visible=True):
        """设置排队名单源的可见性 - 此方法不再需要"""
        logger.debug("OBSController.show_quelist_source called, but OBS text display is disabled.")
    
    def show_congrats(self, player: str, item: str):
        """显示祝贺特效 - 只处理OBS视频显示"""
        logger.info(f"显示祝贺特效视频：玩家={player}, 物品={item}")
        
        if not self.connected:
            self.connect_to_obs()
            if not self.connected:
                return False
    
        lock_acquired = False
        try:
            lock_acquired = self.lock.acquire(timeout=3)
            if not lock_acquired:
                logger.warning("获取OBS锁超时，跳过祝贺视频显示")
                return False
            
            try:
                # 显示祝贺视频源
                self._update_source_visibility(self.congrats_video_source, True)
                
                # 设置定时器隐藏祝贺
                def hide_after_delay():
                    time.sleep(self.congrats_duration)
                    self.hide_congrats()
                
                hide_thread = threading.Thread(target=hide_after_delay, daemon=True)
                hide_thread.start()
                
                return True
                
            except Exception as e:
                logger.error(f"OBS祝贺视频显示操作失败: {e}")
                return False
                
        finally:
            if lock_acquired:
                self.lock.release()
    
    def hide_congrats(self):
        """隐藏祝贺特效视频"""
        if not self.connected:
            return False
        
        lock_acquired = False
        try:
            lock_acquired = self.lock.acquire(timeout=3)
            if not lock_acquired:
                logger.warning("获取OBS锁超时！放弃隐藏祝贺视频特效")
                return False
            
            try:
                self._update_source_visibility(self.congrats_video_source, False)
                return True
                
            except Exception as e:
                logger.error(f"隐藏祝贺视频特效时出错: {e}")
                return False
                
        finally:
            if lock_acquired:
                self.lock.release()
    
    def _update_source_visibility(self, source_name: str, visible: bool):
        """更新指定源的可见性，假设已持有锁"""
        if not source_name:
            return
            
        # 跳过相同状态
        if source_name in self.visibility_states and self.visibility_states[source_name] == visible:
            return
        
        try:
            scene = self.cached_scene_name
            if not scene:
                response = self.client.get_current_program_scene()
                # 修正: 使用正确的属性访问方式
                scene = response.current_program_scene_name
                self.cached_scene_name = scene
            
            scene_item_id = self.cached_scene_item_ids.get(source_name)
            if scene_item_id is None:
                # 修正: 使用正确的属性访问方式
                scene_items_response = self.client.get_scene_item_list(scene)
                scene_items = scene_items_response.scene_items
                
                for item in scene_items:
                    if item['sourceName'] == source_name:
                        scene_item_id = item['sceneItemId']
                        self.cached_scene_item_ids[source_name] = scene_item_id
                        break
                        
            if scene_item_id is None:
                logger.warning(f"在场景 '{scene}' 中未找到源 '{source_name}'")
                return
                
            self.client.set_scene_item_enabled(scene, scene_item_id, visible)
            self.visibility_states[source_name] = visible
            
        except Exception as e:
            logger.error(f"更新源 '{source_name}' 可见性时出错: {e}")
            self.connected = False
            self.cached_scene_item_ids.pop(source_name, None)
            self.visibility_states.pop(source_name, None)
    
    def _update_source_text(self, source_name: str, text: str):
        """更新指定文本源的内容 - 此方法不再需要"""
        logger.debug(f"OBSController._update_source_text called for {source_name}, but OBS text display is disabled.")

    def show_current_player(self, player_name: str):
        """显示当前正在游戏的玩家提示 - 此方法不再需要"""
        logger.debug(f"OBSController.show_current_player called for {player_name}, but OBS text display is disabled.")
        return True
                
    def hide_current_player(self):
        """隐藏当前玩家提示 - 此方法不再需要"""
        logger.debug("OBSController.hide_current_player called, but OBS text display is disabled.")
        return True

    def update_config(self, new_obs_config: Dict[str, Any]):
        """更新OBS控制器的配置参数（线程安全）"""
        lock_acquired = False
        try:
            lock_acquired = self.lock.acquire(timeout=3)
            if not lock_acquired:
                logger.warning("获取OBS锁超时，跳过配置更新")
                return False

            # 更新可以运行时修改的配置参数
            if 'update_interval' in new_obs_config:
                self.update_interval = new_obs_config['update_interval']
                logger.info(f"OBS更新间隔已更新为: {self.update_interval}")

            if 'congrats_video_source' in new_obs_config:
                old_source = self.congrats_video_source
                self.congrats_video_source = new_obs_config['congrats_video_source']
                # 清除旧源的缓存
                if old_source in self.cached_scene_item_ids:
                    del self.cached_scene_item_ids[old_source]
                if old_source in self.visibility_states:
                    del self.visibility_states[old_source]
                logger.info(f"OBS祝贺视频源已更新为: {self.congrats_video_source}")

            if 'congrats_duration' in new_obs_config:
                self.congrats_duration = new_obs_config['congrats_duration']
                logger.info(f"OBS祝贺持续时间已更新为: {self.congrats_duration}")

            # 如果连接参数发生变化，需要重新连接（但这些参数在配置重新加载中被排除了）
            # 这里只是为了完整性而保留
            connection_changed = False
            if self.config and 'obs' in self.config:
                obs_config = self.config['obs']
                for key in ['host', 'port', 'password']:
                    if key in new_obs_config and new_obs_config[key] != obs_config.get(key):
                        connection_changed = True
                        break

            if connection_changed:
                logger.info("OBS连接参数发生变化，将重新连接")
                self.connected = False
                self._clear_cache()
                self.connect_to_obs()

            return True

        except Exception as e:
            logger.error(f"更新OBS配置时出错: {e}")
            return False
        finally:
            if lock_acquired:
                self.lock.release()

# 创建全局OBS控制器实例
obs_controller = None

def init_obs_controller(config=None):
    """初始化OBS控制器"""
    global obs_controller
    if obs_controller is None:
        logger.info("初始化全局 OBSController")
        obs_controller = OBSController(config)
    return obs_controller

def update_queue_display(in_que, player_info):
    """更新队列显示，供外部调用 - 此函数不再需要更新文本"""
    global obs_controller
    if obs_controller is None:
        logger.warning("obs_controller 未初始化!")
        return
    
    logger.debug("Global update_queue_display called, but OBS text display is disabled.")
