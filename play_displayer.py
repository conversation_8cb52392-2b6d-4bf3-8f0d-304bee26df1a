import tkinter as tk
from tkinter import ttk
import tkinter.font as tkfont
import threading
import time
import queue
from datetime import datetime
import logging
import Play_rec_detect
import socket
import json
import subprocess
import sys
import os

class PlayDisplayer:
    """负责创建和管理一个独立的GUI窗口，用于实时显示游戏直播助手的关键信息"""
    
    def __init__(self, config):
        self.logger = logging.getLogger(__name__)
        
        # 配置
        self.config = config
        self.displayer_config = config.get('displayer', {})
        
        # 内部状态
        self.active_players_lines = []
        self.is_running = True
        self.current_target_object = None
        
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title(self.displayer_config.get('window', {}).get('title', "游戏助手实时信息面板"))
        
        # 设置窗口大小和位置
        width_ratio = self.displayer_config.get('window', {}).get('default_width_ratio', 0.75)
        height_ratio = self.displayer_config.get('window', {}).get('default_height_ratio', 0.6)
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = int(screen_width * width_ratio)
        window_height = int(screen_height * height_ratio)
        
        x_pos = (screen_width - window_width) // 2
        y_pos = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x_pos}+{y_pos}")
        self.root.update_idletasks()
        
        bg_color = self.displayer_config.get('window', {}).get('background_color', "#DDDDDD")
        self.root.configure(bg=bg_color)
        self.bg_color = bg_color
        
        # 创建左右分栏
        self.paned_window = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True)
        
        self.left_frame = ttk.Frame(self.paned_window)
        self.right_frame = ttk.Frame(self.paned_window)
        
        self.paned_window.add(self.left_frame, weight=1)
        self.paned_window.add(self.right_frame, weight=1)
        
        left_panel_ratio = self.displayer_config.get('layout', {}).get('left_panel_ratio', 0.6)
        self.paned_window.sashpos(0, int(window_width * left_panel_ratio))
        
        # 初始化UI组件
        self._init_panels()
        self._load_fonts_and_colors()
        
        # 这些变量是必要的，否则会导致类型错误
        # 在start_displayer_process中需要添加这些变量的初始化
        self.player_info = {}
        self.player_comments = {}
        self.player_games = {}
        self.current_player = {}
        self.player_info_lock = threading.RLock()
        self.games_lock = threading.RLock()
        self.current_player_lock = threading.RLock()
        self.stop_flag = {'running': True}
        self.update_event_queue = queue.Queue()
        
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
        self.logger.info("GUI窗口已初始化")
    
    def _init_panels(self):
        """初始化左右面板的UI组件"""
        # 左侧面板
        self.current_player_text = tk.Text(self.left_frame, wrap=tk.WORD, state=tk.DISABLED, height=4,
                                          bg=self.bg_color, bd=0, highlightthickness=0)
        self.current_player_text.pack(fill=tk.X, padx=10, pady=5)
        
        self.caught_item_label = tk.Label(self.left_frame, text="", anchor="w", justify=tk.LEFT, bg=self.bg_color)
        self.caught_item_label.pack(fill=tk.X, padx=10, pady=5)
        
        # 创建状态信息框架：排队人数和FPS并列显示
        self.status_frame = ttk.Frame(self.left_frame)
        self.status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.queue_size_label = tk.Label(self.status_frame, text="排队: 0", anchor="w", bg=self.bg_color)
        self.queue_size_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.fps_label = tk.Label(self.status_frame, text="FPS: --", anchor="e", bg=self.bg_color)
        self.fps_label.pack(side=tk.RIGHT, fill=tk.X, expand=True)
        
        # 排队玩家列表
        self.queue_frame = ttk.Frame(self.left_frame)
        self.queue_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.queue_text = tk.Text(self.queue_frame, wrap=tk.WORD, state=tk.DISABLED, height=10,
                                  bg="#FFFFFF", fg="#000000")  # 修改：设置白色背景和黑色文字
        self.queue_yscroll = ttk.Scrollbar(self.queue_frame, orient=tk.VERTICAL, command=self.queue_text.yview)
        self.queue_text.configure(yscrollcommand=self.queue_yscroll.set)
        self.queue_xscroll = ttk.Scrollbar(self.queue_frame, orient=tk.HORIZONTAL, command=self.queue_text.xview)
        self.queue_text.configure(xscrollcommand=self.queue_xscroll.set)
        
        self.queue_text.grid(row=0, column=0, sticky="nsew")
        self.queue_yscroll.grid(row=0, column=1, sticky="ns")
        self.queue_xscroll.grid(row=1, column=0, sticky="ew")
        
        self.queue_frame.grid_rowconfigure(0, weight=1)
        self.queue_frame.grid_columnconfigure(0, weight=1)
        
        # 右侧面板
        self.log_frame = ttk.Frame(self.right_frame)
        self.log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        self.log_text = tk.Text(self.log_frame, wrap=tk.WORD, state=tk.DISABLED, height=10)
        self.log_yscroll = ttk.Scrollbar(self.log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=self.log_yscroll.set)
        
        self.log_text.grid(row=0, column=0, sticky="nsew")
        self.log_yscroll.grid(row=0, column=1, sticky="ns")
        
        self.log_frame.grid_rowconfigure(0, weight=1)
        self.log_frame.grid_columnconfigure(0, weight=1)
    
    def _load_fonts_and_colors(self):
        """根据配置加载字体和颜色"""
        self.fonts = {}
        font_configs = self.displayer_config.get('fonts', {})
        
        for section, config in font_configs.items():
            family = config.get('family', 'TkDefaultFont')
            size = config.get('size', 10)
            weight = config.get('weight', 'normal')
            color = config.get('color', '#FFFFFF')
            
            try:
                font = tkfont.Font(family=family, size=size, weight=weight)
                self.fonts[section] = {'font': font, 'color': color}
            except Exception as e:
                self.logger.error(f"Error loading font {family}: {e}")
                font = tkfont.Font(family='TkDefaultFont', size=size, weight=weight)
                self.fonts[section] = {'font': font, 'color': color}
        
        # 应用字体和颜色到UI组件
        bg_color = self.displayer_config.get('window', {}).get('background_color', "#2B2B2B")
        for section, widget in [('current_player', self.current_player_text), ('caught_item', self.caught_item_label),
                               ('queue_size', self.queue_size_label), ('queue_size', self.fps_label), 
                               ('queue_player', self.queue_text), ('active_players', self.log_text)]:
            if section in self.fonts:
                widget.configure(font=self.fonts[section]['font'], fg=self.fonts[section]['color'], bg=bg_color)
    
    def _format_line_for_display(self, data_dict, field_widths_config, prefix=""):
        """通用文本行格式化"""
        formatted_parts = []
        for field, value in data_dict.items():
            if field == 'target_object':
                continue
            
            # 确保所有值都是字符串类型
            value_str = str(value) if value is not None else ""
            
            if field == 'name':
                width_key = 'name_width'
                prefixed = f"{prefix}_{width_key}" if prefix else width_key
                width = field_widths_config.get(prefixed, len(value_str))
                formatted_value = value_str.ljust(width)
            elif field == 'history_rewards':
                formatted_value = value_str
            else:
                formatted_value = value_str
            
            formatted_parts.append(formatted_value)
        return ", ".join(formatted_parts)
    
    def _update_current_player_display(self, data=None):
        """更新当前玩家信息的显示"""
        try:
            if data and data.get('player_id'):
                # 为虚拟玩家添加[托]标识
                player_name = data.get('name', '')
                player_id = data.get('player_id', '')
                if "VirtualPlayer" in player_id:
                    player_name = f"[托]{player_name}"

                display_data = {
                    'name': player_name,
                    'target_id': f"目标: {data.get('target_id', '')}",
                    'target_object': f"物品: {data.get('target_object', '')}",
                    'comments_count': f"评论: {data.get('comments_count', 0)}",
                    'games_played': f"游戏次数: {data.get('games_played', 0)}",
                    'history_rewards': f"历史奖励: {data.get('history_rewards', '')}"
                }
                text_cfg = self.displayer_config.get('text_formatting', {})
                s = self._format_line_for_display(display_data, text_cfg, prefix="current_player")
                self.current_player_text.configure(state=tk.NORMAL)
                self.current_player_text.delete(1.0, tk.END)
                self.current_player_text.insert(tk.END, s)
                self.current_player_text.configure(state=tk.DISABLED)
            else:
                self.current_player_text.configure(state=tk.NORMAL)
                self.current_player_text.delete(1.0, tk.END)
                self.current_player_text.configure(state=tk.DISABLED)
        except Exception as e:
            self.logger.error(f"Error updating current player display: {e}")

    def _update_caught_item_display(self, item_name=None):
        """更新抓取到的物品的显示"""
        try:
            self.caught_item_label.configure(text=f"抓到: {item_name}" if item_name else "")
        except Exception as e:
            self.logger.error(f"Error updating caught item display: {e}")
    
    def _update_queue_size_display(self, queue_size=0):
        """更新排队人数的显示"""
        try:
            self.queue_size_label.configure(text=f"当前排队人数: {queue_size}")
        except Exception as e:
            self.logger.error(f"Error updating queue size display: {e}")
    
    def _update_fps_display(self):
        """更新FPS显示（已弃用，保留用于兼容性）"""
        # 这个方法现在主要用于初始化显示，实际FPS数据通过TCP接收
        try:
            self.fps_label.configure(text="FPS: --")
        except Exception as e:
            self.logger.error(f"Error updating FPS display: {e}")
            self.fps_label.configure(text="FPS: ??")

    def _update_fps_display_from_tcp(self, fps, data_age):
        """从TCP接收的FPS数据更新显示"""
        try:
            # 检查数据新鲜度，超过2秒认为过期
            if data_age <= 2.0:
                self.fps_label.configure(text=f"FPS: {fps:.1f}")
            else:
                self.fps_label.configure(text="FPS: --")
        except Exception as e:
            self.logger.error(f"Error updating FPS display from TCP: {e}")
            self.fps_label.configure(text="FPS: ??")
    
    def _update_player_queue_display(self, queue_data=None, player_info_data=None):
        """更新排队玩家列表的显示"""
        try:
            self.queue_text.configure(state=tk.NORMAL)
            self.queue_text.delete(1.0, tk.END)

            if not queue_data: # 如果 queue_data 是 None 或者空列表
                self.queue_text.insert(tk.END, "排队列表为空\n") # 在GUI上给出明确提示
                self.queue_text.configure(state=tk.DISABLED)
                return
                
            text_format_config = self.displayer_config.get('text_formatting', {})
            
            item_displayed_count = 0
            for i, queue_item in enumerate(queue_data):
                try:
                    # queue_item 现在是 (entry_list, possibility, priority[, order_id])
                    if len(queue_item) == 4:
                        entry, possibility, priority, order_id = queue_item
                    else:
                        entry, possibility, priority = queue_item
                        order_id = None
                    
                    # 直接使用索引访问player_id，避免解包整个entry
                    if len(entry) >= 4:
                        player_id = entry[3]
                        name = entry[2]
                    else:
                        continue  # 跳过格式不正确的条目
                    
                    if player_id:
                        player_data = player_info_data.get(player_id, {})
                        target_id = entry[4] if len(entry) > 4 else ""
                        target_object = entry[6] if len(entry) > 6 else f"未知({target_id})"
                        comments_count = player_data.get('comments_after_game', 0)
                        games_played = len(player_data.get('games', []))
                        
                        rank_str = f"{i+1}："
                        
                        # 显示目标ID和物体名称
                        target_display = f"{target_id}"
                        if target_object and target_object != f"未知({target_id})":
                            target_display += f"({target_object})"
                        
                        # 如果是付费玩家，在名字前加标记
                        display_name = name
                        # 如果是虚拟玩家，在名字前加[托]标记
                        if "VirtualPlayer" in player_id:
                            display_name = f"[托]{display_name}"
                        elif order_id is not None:
                            display_name = f"[付费]{display_name}"
                        
                        rest_data = {
                            'name': display_name, 
                            'target_id': target_display,
                            'comments_count': comments_count, 
                            'games_played': games_played, 
                            'history_rewards': ""
                        }
                        rest_formatted = self._format_line_for_display(rest_data, text_format_config, prefix="queue")
                        line = f"{rank_str}{rest_formatted}\n"
                        self.queue_text.insert(tk.END, line)
                        item_displayed_count += 1
                except (ValueError, IndexError, TypeError) as e:
                    self.logger.error(f"GUI_DISPLAYER: 处理队列项目 {i} 时出错: {e}, queue_item: {str(queue_item)[:100]}", exc_info=True)
                    continue
            
            if item_displayed_count == 0 and queue_data:
                 self.queue_text.insert(tk.END, "无法处理队列数据项\n")

            self.queue_text.configure(state=tk.DISABLED)
        except Exception as e:
            self.logger.error(f"GUI_DISPLAYER: Error updating player queue display: {e}", exc_info=True)
    
    def _add_active_player_event(self, event_data):
        """添加玩家活动事件到右侧日志"""
        try:
            player_id = event_data.get('player_id')
            comment_text = event_data.get('comment_text', '')
            comment_dt = event_data.get('comment_dt')
            total_comments = event_data.get('total_comments', 0)
            total_games = event_data.get('total_games', 0)
            
            if not player_id or not comment_dt:
                return
            
            # 截断过长的文本
            max_name_length = self.displayer_config.get('text_formatting', {}).get('max_active_name_length', 10)
            max_comment_length = self.displayer_config.get('text_formatting', {}).get('max_active_comment_length', 50)
            
            player_name = event_data.get('player_name', '')
            player_name = self._truncate_text(player_name, max_name_length)
            comment_text = self._truncate_text(comment_text, max_comment_length)
            
            # 计算停留时长
            duration = "1分钟"
            
            # 格式化活动日志行
            log_line = f"{player_name}，评{total_comments}，停{duration}，玩{total_games}，评：{comment_text}"
            
            self.active_players_lines.insert(0, log_line)
            
            max_lines = self.displayer_config.get('active_players_max_lines', 30)
            if len(self.active_players_lines) > max_lines:
                self.active_players_lines = self.active_players_lines[:max_lines]
            
            # 更新活动日志显示
            self.log_text.configure(state=tk.NORMAL)
            self.log_text.delete(1.0, tk.END)
            for line in self.active_players_lines:
                self.log_text.insert(tk.END, line + "\n")
            self.log_text.configure(state=tk.DISABLED)
        except Exception as e:
            self.logger.error(f"Error adding active player event: {e}")
    
    def _get_formatted_player_details(self, player_id, is_current_player=False, game_content=None):
        """获取玩家详细信息"""
        result = {'name': '', 'target_id': '', 'target_object': '', 'comments_count': '0', 'duration': '', 'games_played': '0', 'history_rewards': ''}
        
        try:
            with self.player_info_lock:
                if player_id in self.player_info:
                    player_data_info = self.player_info[player_id]
                    result['name'] = player_data_info.get('name', '')
                    result['comments_count'] = str(player_data_info.get('comments_after_game', 0))
            
            # 获取游戏次数和历史奖励
            history_rewards = []
            with self.games_lock:
                if player_id in self.player_games:
                    result['games_played'] = str(len(self.player_games[player_id]))
                    for game in self.player_games[player_id]:
                        if len(game) > 1 and game[1] and game[1].lower() != 'nothing':
                            history_rewards.append(game[1])
            
            max_rewards_length = self.displayer_config.get('text_formatting', {}).get('max_history_rewards_length', 50)
            result['history_rewards'] = self._truncate_text("、".join(history_rewards), max_rewards_length)
            
            # 处理目标ID和目标物体名称
            if is_current_player:
                with self.current_player_lock:
                    target_id = self.current_player.get('target_id', '')
                    result['target_id'] = str(target_id)
                    
                    if self.current_target_object:
                        result['target_object'] = self.current_target_object
                    else:
                        target_object = self.current_player.get('target_object', '')
                        result['target_object'] = target_object if target_object else (f"未知({target_id})" if target_id else "")
            elif game_content:
                # game_content 现在是 (entry_list, possibility, priority[, order_id])
                try:
                    if isinstance(game_content, tuple) and len(game_content) >= 1:
                        entry = game_content[0]
                        if isinstance(entry, list) and len(entry) >= 5:
                            # 使用直接索引访问，避免解包长度问题
                            target_id = entry[4]  # content字段
                            result['target_id'] = str(target_id)
                            # 从entry中获取物体名称（如果有的话）
                            if len(entry) >= 7:
                                result['target_object'] = entry[6]  # target_object_name字段
                            else:
                                result['target_object'] = f"未知({target_id})"
                except (IndexError, TypeError) as e:
                    self.logger.error(f"解析游戏内容时出错: {e}")
                    result['target_object'] = "未知"
            
            return result
        except Exception as e:
            self.logger.error(f"Error getting formatted player details: {e}")
            return result
    
    def _truncate_text(self, text, max_len):
        """截断文本以确保它不超过最大长度"""
        if not text:
            return ""
        if len(text) <= max_len:
            return text
        return text[:max_len-3] + "..."
    
    def _process_gui_updates(self):
        """处理来自主应用程序的GUI更新事件"""
        try:
            if not self.stop_flag.get('running', True) or not self.is_running:
                return
            
            try:
                while True:
                    event = self.update_event_queue.get_nowait()
                    event_type = event.get('type')
                    # 降低日志级别，避免频繁输出
                    if event_type != 'queue_update':
                        self.logger.debug(f"Received GUI update event: {event_type}")
                    
                    if event_type == 'queue_update':
                        self._update_queue_size_display(event.get('queue_size', 0))
                        self._update_player_queue_display(event.get('queue_data'), event.get('player_info_data'))
                    elif event_type == 'game_start':
                        self.current_target_object = event.get('target_object', '')
                        self._update_current_player_display(event)
                        self._update_caught_item_display()
                    elif event_type == 'game_end':
                        self.current_target_object = None
                        self._update_current_player_display()
                        self._update_caught_item_display()
                    elif event_type == 'object_picked':  # 修改：从 'caught' 改为 'object_picked'
                        item_name = event.get('item_name', '')
                        self._update_caught_item_display(item_name)
                    elif event_type == 'new_comment':
                        self._add_active_player_event(event)
                    
                    self.update_event_queue.task_done()
            except queue.Empty:
                pass
            
            # FPS显示现在通过TCP更新，不需要在这里更新
            
            if self.stop_flag.get('running', True) and self.is_running:
                self.root.after(30, self._process_gui_updates)
        except Exception as e:
            self.logger.error(f"Error processing GUI updates: {e}", exc_info=True)
            if self.stop_flag.get('running', True) and self.is_running:
                self.root.after(30, self._process_gui_updates)
    
    def _on_closing(self):
        """处理窗口关闭事件"""
        self.logger.info("GUI窗口关闭")
        self.is_running = False
        self.root.destroy()
    
    def run(self):
        """启动GUI"""
        self.logger.info("启动GUI")
        
        # 初始化显示
        self._update_queue_size_display()
        self._update_player_queue_display()
        self._update_current_player_display()
        
        self.root.mainloop()

class DisplayerTCPServer:
    """TCP服务器，接收主进程的更新命令并更新GUI"""
    
    def __init__(self, port, displayer, stop_flag):
        self.port = port
        self.displayer = displayer
        self.stop_flag = stop_flag
        self.server_socket = None
        self.logger = logging.getLogger(__name__)
        
    def start(self):
        """启动TCP服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('localhost', self.port))
            self.server_socket.listen(1)
            self.server_socket.settimeout(1.0)  # 设置超时以便能够检查stop_flag
            
            self.logger.info(f"GUI TCP服务器已启动，监听端口 {self.port}")
            
            while self.stop_flag.get('running', True):
                try:
                    client_socket, address = self.server_socket.accept()
                    self.logger.info(f"接受来自 {address} 的连接")
                    
                    # 在新线程中处理客户端
                    client_thread = threading.Thread(
                        target=self._handle_client, 
                        args=(client_socket,), 
                        daemon=True
                    )
                    client_thread.start()
                    
                except socket.timeout:
                    continue
                except Exception as e:
                    if self.stop_flag.get('running', True):
                        self.logger.error(f"接受连接时出错: {e}")
                    break
                    
        except Exception as e:
            self.logger.error(f"启动TCP服务器失败: {e}")
        finally:
            if self.server_socket:
                self.server_socket.close()
                
    def _handle_client(self, client_socket):
        """处理客户端连接"""
        try:
            buffer = ""
            while self.stop_flag.get('running', True):
                data = client_socket.recv(4096).decode('utf-8')
                if not data:
                    break
                    
                buffer += data
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    if line:
                        self._process_command(line)
                        
        except Exception as e:
            self.logger.error(f"处理客户端时出错: {e}")
        finally:
            client_socket.close()
            
    def _process_command(self, command_line):
        """处理接收到的命令"""
        try:
            command = json.loads(command_line)
            cmd_type = command.get('type')
            
            # 在主线程中执行GUI更新
            if cmd_type == 'queue_update':
                q_data = command.get('queue_data')
                p_info_data = command.get('player_info_data')

                def update_queue():
                    self.displayer._update_queue_size_display(command.get('queue_size', 0))
                    self.displayer._update_player_queue_display(
                        q_data, # 使用捕获的变量
                        p_info_data # 使用捕获的变量
                    )
                self.displayer.root.after(0, update_queue)
                
            elif cmd_type == 'game_start':
                def update_game_start():
                    self.displayer.current_target_object = command.get('target_object', '')
                    self.displayer._update_current_player_display(command)
                    self.displayer._update_caught_item_display()
                self.displayer.root.after(0, update_game_start)
                
            elif cmd_type == 'game_end':
                def update_game_end():
                    self.displayer.current_target_object = None
                    self.displayer._update_current_player_display()
                    # self.displayer._update_caught_item_display()
                self.displayer.root.after(0, update_game_end)
                
            elif cmd_type == 'object_picked':  # 修改：从 'caught' 改为 'object_picked'
                item_name = command.get('item_name', '')
                # 使用默认参数确保闭包正确捕获变量值
                self.displayer.root.after(0, lambda name=item_name: self.displayer._update_caught_item_display(name))
                
            elif cmd_type == 'fps_update':
                fps = command.get('fps', 0)
                data_age = command.get('data_age', 0)
                # 使用默认参数确保闭包正确捕获变量值
                self.displayer.root.after(0, lambda f=fps, age=data_age: self.displayer._update_fps_display_from_tcp(f, age))

            elif cmd_type == 'new_comment':
                self.displayer.root.after(0, lambda: self.displayer._add_active_player_event(command))

            elif cmd_type == 'exit':
                self.stop_flag['running'] = False
                self.displayer.root.after(0, self.displayer.root.quit)
                
        except json.JSONDecodeError as e:
            self.logger.error(f"解析命令失败: {e}, 命令: {command_line}")
        except Exception as e:
            self.logger.error(f"处理命令时出错: {e}")

def start_displayer_process(config):
    """启动独立的GUI进程"""
    # 初始化日志记录器和停止标志
    logger = None
    stop_flag = {'running': True}
    
    try:
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - [%(threadName)s] - %(levelname)s - %(message)s'
        )
        
        logger = logging.getLogger(__name__)
        logger.info("GUI进程启动")
        
        # 创建GUI
        displayer = PlayDisplayer(config)
        
        # 设置停止标志
        displayer.stop_flag = stop_flag
        
        # 设置必要的内部变量
        # 添加必要的变量初始化，避免属性访问错误
        displayer.player_info = {}
        displayer.player_comments = {}
        displayer.player_games = {}
        displayer.current_player = {}
        displayer.player_info_lock = threading.RLock()
        displayer.games_lock = threading.RLock()
        displayer.current_player_lock = threading.RLock()
        displayer.update_event_queue = queue.Queue()
        
        # 启动TCP服务器
        tcp_server = DisplayerTCPServer(5557, displayer, stop_flag)
        server_thread = threading.Thread(target=tcp_server.start, daemon=True)
        server_thread.start()
        
        # FPS显示现在通过TCP更新，不需要定时器
        
        # 运行GUI主循环
        displayer.run()
        
    except Exception as e:
        if logger:
            logger.error(f"GUI进程出错: {e}")
        else:
            print(f"GUI进程出错: {e}")
    finally:
        stop_flag['running'] = False
        if logger:
            logger.info("GUI进程结束")
        else:
            print("GUI进程结束")

class DisplayerClient:
    """客户端，向GUI进程发送更新命令"""
    
    def __init__(self, port=5557):
        self.port = port
        self.socket = None
        self.logger = logging.getLogger(__name__)
        self.process = None
        
    def start_process(self, config):
        """启动GUI进程"""
        try:
            # 获取当前脚本路径
            current_script = os.path.abspath(__file__)
            
            # 启动子进程
            self.process = subprocess.Popen([
                sys.executable, current_script, '--gui-process'
            ], env={**os.environ, 'GUI_CONFIG': json.dumps(config)})
            
            self.logger.info(f"GUI进程已启动，PID: {self.process.pid}")
            
            # 等待GUI进程启动
            time.sleep(2)
            
            # 连接到GUI进程
            return self._connect()
            
        except Exception as e:
            self.logger.error(f"启动GUI进程失败: {e}")
            return False
            
    def _connect(self):
        """连接到GUI进程"""
        for attempt in range(10):
            try:
                self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.socket.connect(('localhost', self.port))
                self.logger.info("已连接到GUI进程")
                return True
            except ConnectionRefusedError:
                time.sleep(0.5)
                if attempt == 9:
                    self.logger.error("连接GUI进程失败")
                    return False
        return False
    
    def send_update(self, update_data):
        """发送更新数据到GUI进程"""
        if not self.socket:
            self.logger.warning("DISPLAYER_CLIENT: Socket not connected, cannot send update.")
            return False
            
        try:
            message = json.dumps(update_data) + '\n'
            self.socket.send(message.encode('utf-8'))
            return True
        except Exception as e:
            self.logger.error(f"发送更新失败: {e}", exc_info=True)
            self.logger.info("DISPLAYER_CLIENT: Attempting to reconnect due to send error...")
            self._connect()
            return False
    
    def destroy(self):
        """关闭连接并结束GUI进程"""
        try:
            if self.socket:
                self.send_update({'type': 'exit'})
                self.socket.close()
                self.socket = None
                
            if self.process:
                self.process.terminate()
                self.process.wait(timeout=5)
                self.logger.info("GUI进程已终止")
        except Exception as e:
            self.logger.error(f"关闭GUI进程时出错: {e}")

# 入口点：如果作为独立进程运行
if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--gui-process':
        # 从环境变量读取配置
        config_json = os.environ.get('GUI_CONFIG', '{}')
        config = json.loads(config_json)
        start_displayer_process(config)
    else:
        print("此文件应该通过主程序启动")
